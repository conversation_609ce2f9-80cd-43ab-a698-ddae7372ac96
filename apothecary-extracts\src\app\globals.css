@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400&family=Source+Sans+Pro:ital,wght@0,300;0,400;0,600;0,700;1,400&family=Playfair+Display:ital,wght@0,400;0,700;1,400&display=swap');

:root {
  /* Primary Colors */
  --color-primary-sage: #87A96B;
  --color-primary-forest: #4A5D23;
  --color-primary-mint: #A8C090;

  /* Secondary Colors */
  --color-secondary-amber: #D4A574;
  --color-secondary-clay: #B8956A;
  --color-secondary-lavender: #9B8AA3;

  /* Neutral Colors */
  --color-neutral-white: #FFFFFF;
  --color-neutral-cream: #F7F5F3;
  --color-neutral-stone: #E5E1DB;
  --color-neutral-charcoal: #3C3C3C;
  --color-neutral-black: #1A1A1A;

  /* Status Colors */
  --color-success: #6B8E23;
  --color-warning: #DAA520;
  --color-error: #A0522D;
  --color-info: #708090;

  /* Legacy support */
  --background: var(--color-neutral-cream);
  --foreground: var(--color-neutral-charcoal);
}

@theme inline {
  /* Colors */
  --color-primary-sage: #87A96B;
  --color-primary-forest: #4A5D23;
  --color-primary-mint: #A8C090;
  --color-secondary-amber: #D4A574;
  --color-secondary-clay: #B8956A;
  --color-secondary-lavender: #9B8AA3;
  --color-neutral-white: #FFFFFF;
  --color-neutral-cream: #F7F5F3;
  --color-neutral-stone: #E5E1DB;
  --color-neutral-charcoal: #3C3C3C;
  --color-neutral-black: #1A1A1A;
  --color-success: #6B8E23;
  --color-warning: #DAA520;
  --color-error: #A0522D;
  --color-info: #708090;
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  /* Typography */
  --font-primary: 'Crimson Text', Georgia, serif;
  --font-secondary: 'Source Sans Pro', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-accent: 'Playfair Display', Georgia, serif;
  --font-sans: var(--font-secondary);
  --font-serif: var(--font-primary);

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;

  /* Border Radius */
  --radius-sm: 0.125rem;
  --radius-base: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-secondary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--color-primary-sage);
  outline-offset: 2px;
}

/* Remove focus outline for mouse users */
*:focus:not(:focus-visible) {
  outline: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-neutral-stone);
}

::-webkit-scrollbar-thumb {
  background: var(--color-primary-sage);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-forest);
}
