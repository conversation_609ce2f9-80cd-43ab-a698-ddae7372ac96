import { Hero } from "@/components/content/Hero";
import { ProductGrid } from "@/components/content/ProductGrid";
import { Section } from "@/components/layout/Section";
import { productCategories } from "@/data/products";

export default function Home() {
  return (
    <>
      {/* Hero Section */}
      <Hero
        subtitle="Premium Medicinal Cannabis"
        title="Where Traditional Apothecary Meets Modern Cannabis Science"
        description="Discover our carefully curated selection of artisanal cannabis products, crafted with precision and care for your wellness journey."
        backgroundImage="https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80"
        primaryCTA={{
          text: "Shop Products",
          href: "/shop",
          icon: "leaf"
        }}
        secondaryCTA={{
          text: "Learn More",
          href: "/about",
          icon: "info"
        }}
      />

      {/* Product Categories Section */}
      <Section padding="xl" background="cream">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-neutral-charcoal mb-4 font-accent">
            Explore Our Premium Selection
          </h2>
          <p className="text-lg text-neutral-charcoal/70 max-w-2xl mx-auto">
            From traditional flower to innovative extracts, discover cannabis products
            that meet the highest standards of quality and purity.
          </p>
        </div>

        <ProductGrid
          products={productCategories}
          columns={3}
          gap="lg"
          showAll={false}
        />
      </Section>

      {/* Mission Statement Section */}
      <Section
        padding="xl"
        background="sage"
        className="text-center"
      >
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-neutral-white mb-6 font-accent">
            Healing Through Nature's Wisdom
          </h2>
          <p className="text-xl text-neutral-white/90 mb-8 leading-relaxed">
            At Apothecary Extracts, we honor the ancient tradition of botanical medicine
            while embracing modern scientific understanding. Our mission is to provide
            premium cannabis products that support your health and wellness journey
            with the respect and care that nature deserves.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/about"
              className="inline-flex items-center px-8 py-3 text-lg font-medium text-primary-sage bg-neutral-white rounded-md hover:bg-neutral-cream transition-colors duration-200"
            >
              Our Story
            </a>
            <a
              href="/locations"
              className="inline-flex items-center px-8 py-3 text-lg font-medium text-neutral-white border border-neutral-white rounded-md hover:bg-neutral-white hover:text-primary-sage transition-colors duration-200"
            >
              Visit Our Dispensaries
            </a>
          </div>
        </div>
      </Section>

      {/* Educational Section */}
      <Section padding="xl" background="white">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-charcoal mb-6 font-accent">
              Cannabis Education & Guidance
            </h2>
            <p className="text-lg text-neutral-charcoal/70 mb-6">
              Whether you&apos;re new to cannabis or an experienced user, our knowledgeable
              staff provides personalized consultations to help you find the right
              products for your specific needs and wellness goals.
            </p>
            <ul className="space-y-3 text-neutral-charcoal/70 mb-8">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-primary-sage rounded-full mr-3"></span>
                Personalized product recommendations
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-primary-sage rounded-full mr-3"></span>
                Dosage guidance and safety education
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-primary-sage rounded-full mr-3"></span>
                Monthly educational workshops
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-primary-sage rounded-full mr-3"></span>
                Comprehensive strain and product information
              </li>
            </ul>
            <a
              href="/education"
              className="inline-flex items-center px-6 py-3 text-base font-medium text-neutral-white bg-primary-sage rounded-md hover:bg-primary-forest transition-colors duration-200"
            >
              Learn More
            </a>
          </div>
          <div className="relative">
            <div className="aspect-square bg-gradient-to-br from-primary-sage to-primary-forest rounded-lg flex items-center justify-center">
              <div className="text-center text-neutral-white">
                <div className="w-16 h-16 bg-neutral-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🌿</span>
                </div>
                <p className="text-lg font-medium">Educational Resources</p>
                <p className="text-sm opacity-80">Coming Soon</p>
              </div>
            </div>
          </div>
        </div>
      </Section>
    </>
  );
}
