export interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: 'general' | 'medical' | 'recreational' | 'products' | 'legal' | 'ordering';
  order: number;
}

export const faqData: FAQItem[] = [
  {
    id: 'faq_1',
    question: 'What do I need to purchase cannabis at Apothecary Extracts?',
    answer: 'For recreational purchases, you must be 21 or older with a valid government-issued photo ID. For medical purchases, you need to be 18 or older with a valid Massachusetts medical marijuana card and photo ID. We accept driver\'s licenses, passports, and state-issued ID cards.',
    category: 'general',
    order: 1,
  },
  {
    id: 'faq_2',
    question: 'What are the purchase limits for recreational cannabis?',
    answer: 'Massachusetts law allows recreational customers to purchase up to 1 ounce (28 grams) of flower, 5 grams of concentrates, or equivalent amounts in edibles per day. These limits apply across all dispensaries statewide, not per location.',
    category: 'recreational',
    order: 2,
  },
  {
    id: 'faq_3',
    question: 'What are the purchase limits for medical patients?',
    answer: 'Medical marijuana patients can purchase up to 10 ounces of flower every 60 days, with higher limits for concentrates and edibles. Your specific limits are determined by your physician and listed on your medical marijuana card.',
    category: 'medical',
    order: 3,
  },
  {
    id: 'faq_4',
    question: 'Do you offer delivery services?',
    answer: 'Yes! We offer delivery service within a 20-mile radius of our Downtown Boston location. Delivery is available Tuesday through Sunday, with same-day delivery for orders placed before 2 PM. A $10 delivery fee applies, with free delivery on orders over $100.',
    category: 'ordering',
    order: 4,
  },
  {
    id: 'faq_5',
    question: 'Can I pre-order products online?',
    answer: 'Absolutely! You can browse our full inventory and place orders online for pickup at either location. Pre-orders are held for 24 hours and help ensure your desired products are available when you arrive.',
    category: 'ordering',
    order: 5,
  },
  {
    id: 'faq_6',
    question: 'What payment methods do you accept?',
    answer: 'We accept cash, debit cards, and CanPay (a cannabis-specific digital payment app). Due to federal banking regulations, we cannot accept credit cards. ATMs are available at both locations for your convenience.',
    category: 'general',
    order: 6,
  },
  {
    id: 'faq_7',
    question: 'Do you offer discounts or loyalty programs?',
    answer: 'Yes! We offer a 10% discount for veterans, seniors (65+), and financial hardship patients. Our loyalty program rewards frequent customers with points for every purchase. First-time customers receive 15% off their initial purchase.',
    category: 'general',
    order: 7,
  },
  {
    id: 'faq_8',
    question: 'How do I know which products are right for me?',
    answer: 'Our knowledgeable staff provides personalized consultations to help you find the right products for your needs. We consider your experience level, desired effects, consumption preferences, and any medical conditions. We also offer educational workshops monthly.',
    category: 'products',
    order: 8,
  },
  {
    id: 'faq_9',
    question: 'Are your products tested for safety and potency?',
    answer: 'Yes, all our products undergo rigorous third-party testing for potency, pesticides, heavy metals, residual solvents, and microbials. Test results are available for every product and can be viewed on our website or requested in-store.',
    category: 'products',
    order: 9,
  },
  {
    id: 'faq_10',
    question: 'Can I consume cannabis products in public?',
    answer: 'No, Massachusetts law prohibits consuming cannabis in any public place, including streets, parks, beaches, and businesses. Consumption is only allowed in private residences or designated consumption areas (where permitted by local ordinances).',
    category: 'legal',
    order: 10,
  },
  {
    id: 'faq_11',
    question: 'How should I store my cannabis products?',
    answer: 'Store cannabis in a cool, dry, dark place away from children and pets. Use airtight containers to maintain freshness and potency. Edibles should be kept in their original packaging and stored according to label instructions. Never leave products in hot cars.',
    category: 'products',
    order: 11,
  },
  {
    id: 'faq_12',
    question: 'What\'s the difference between indica, sativa, and hybrid strains?',
    answer: 'Traditionally, indica strains are associated with relaxing, sedating effects; sativa strains with energizing, uplifting effects; and hybrids combine characteristics of both. However, individual terpene profiles and cannabinoid ratios are better predictors of effects than strain type alone.',
    category: 'products',
    order: 12,
  },
  {
    id: 'faq_13',
    question: 'How long do edibles take to work?',
    answer: 'Edibles typically take 30 minutes to 2 hours to take effect, with peak effects occurring 2-4 hours after consumption. Effects can last 4-8 hours or longer. Start with a low dose (2.5-5mg THC) and wait at least 2 hours before consuming more.',
    category: 'products',
    order: 13,
  },
  {
    id: 'faq_14',
    question: 'Can I return or exchange products?',
    answer: 'Due to state regulations, we cannot accept returns or exchanges on cannabis products once they leave the premises. However, if you receive a defective product, please contact us immediately and we\'ll work with you to resolve the issue.',
    category: 'general',
    order: 14,
  },
  {
    id: 'faq_15',
    question: 'Do you offer educational resources for new users?',
    answer: 'Yes! We provide comprehensive educational resources including dosing guides, consumption method explanations, and strain information. We also host monthly educational workshops covering topics like microdosing, terpenes, and cannabis wellness.',
    category: 'general',
    order: 15,
  },
];

export const getFAQByCategory = (category: FAQItem['category']): FAQItem[] => {
  return faqData
    .filter(item => item.category === category)
    .sort((a, b) => a.order - b.order);
};

export const getAllFAQCategories = (): FAQItem['category'][] => {
  return Array.from(new Set(faqData.map(item => item.category)));
};

export const searchFAQ = (query: string): FAQItem[] => {
  const lowercaseQuery = query.toLowerCase();
  return faqData.filter(item => 
    item.question.toLowerCase().includes(lowercaseQuery) ||
    item.answer.toLowerCase().includes(lowercaseQuery)
  );
};

export const getFeaturedFAQ = (): FAQItem[] => {
  return faqData
    .filter(item => item.order <= 8)
    .sort((a, b) => a.order - b.order);
};
