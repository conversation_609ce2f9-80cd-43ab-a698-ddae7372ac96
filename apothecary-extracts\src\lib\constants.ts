// App Configuration
export const APP_CONFIG = {
  name: 'Apothecary Extracts',
  description: 'Premium medicinal cannabis dispensary specializing in artisanal extracts and healing botanicals',
  url: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
  version: '1.0.0',
} as const;

// Contact Information
export const CONTACT_INFO = {
  email: '<EMAIL>',
  phone: '(*************',
  supportEmail: '<EMAIL>',
  businessHours: {
    weekdays: '10:00 AM - 8:00 PM',
    weekends: '10:00 AM - 6:00 PM',
  },
} as const;

// Social Media Links
export const SOCIAL_LINKS = {
  facebook: 'https://facebook.com/apothecaryextracts',
  instagram: 'https://instagram.com/apothecaryextracts',
  twitter: 'https://twitter.com/apothecaryextracts',
  youtube: 'https://youtube.com/@apothecaryextracts',
  linkedin: 'https://linkedin.com/company/apothecary-extracts',
} as const;

// App Store Links
export const APP_STORE_LINKS = {
  ios: 'https://apps.apple.com/us/app/apothecary-extracts',
  android: 'https://play.google.com/store/apps/details?id=com.apothecaryextracts',
} as const;

// Legal Age Requirements
export const LEGAL_AGE = {
  recreational: 21,
  medical: 18,
} as const;

// Purchase Limits (in grams)
export const PURCHASE_LIMITS = {
  recreational: {
    flower: 28, // 1 oz
    concentrates: 5,
    edibles: 28, // equivalent
  },
  medical: {
    flower: 280, // 10 oz per 60 days
    concentrates: 50,
    edibles: 280, // equivalent
  },
} as const;

// Tax Rates (percentage)
export const TAX_RATES = {
  recreational: 20,
  medical: 6.25,
  excise: 10.75,
} as const;

// Product Categories
export const PRODUCT_CATEGORIES = [
  'flower',
  'pre-rolls',
  'concentrates',
  'edibles',
  'tinctures',
  'topicals',
  'vaporizers',
  'accessories',
  'apparel',
] as const;

// Cannabis Types
export const CANNABIS_TYPES = [
  'sativa',
  'indica',
  'hybrid',
  'cbd',
  'high-cbd',
] as const;

// Consumption Methods
export const CONSUMPTION_METHODS = [
  'smoking',
  'vaping',
  'edibles',
  'tinctures',
  'topicals',
  'dabbing',
] as const;

// Effects
export const EFFECTS = [
  'relaxing',
  'energizing',
  'euphoric',
  'creative',
  'focused',
  'sleepy',
  'happy',
  'uplifted',
  'calm',
  'pain-relief',
  'anti-inflammatory',
  'appetite-stimulant',
] as const;

// Terpenes
export const TERPENES = [
  'myrcene',
  'limonene',
  'pinene',
  'linalool',
  'caryophyllene',
  'humulene',
  'terpinolene',
  'ocimene',
  'bisabolol',
  'valencene',
] as const;

// Potency Levels
export const POTENCY_LEVELS = {
  low: { min: 0, max: 15 },
  medium: { min: 15, max: 25 },
  high: { min: 25, max: 35 },
  'very-high': { min: 35, max: 100 },
} as const;

// Experience Levels
export const EXPERIENCE_LEVELS = [
  'beginner',
  'intermediate',
  'experienced',
  'expert',
] as const;

// Medical Conditions
export const MEDICAL_CONDITIONS = [
  'chronic-pain',
  'anxiety',
  'depression',
  'insomnia',
  'epilepsy',
  'cancer',
  'glaucoma',
  'ptsd',
  'arthritis',
  'migraines',
  'nausea',
  'appetite-loss',
] as const;

// Delivery Methods
export const DELIVERY_METHODS = [
  'pickup',
  'delivery',
  'curbside',
] as const;

// Payment Methods
export const PAYMENT_METHODS = [
  'cash',
  'debit',
  'credit',
  'digital-wallet',
  'cryptocurrency',
] as const;

// Order Status
export const ORDER_STATUS = [
  'pending',
  'confirmed',
  'preparing',
  'ready',
  'completed',
  'cancelled',
] as const;

// Membership Tiers
export const MEMBERSHIP_TIERS = [
  'bronze',
  'silver',
  'gold',
  'platinum',
] as const;

// Discount Types
export const DISCOUNT_TYPES = [
  'percentage',
  'fixed-amount',
  'buy-one-get-one',
  'bulk-discount',
  'loyalty-points',
] as const;

// API Endpoints
export const API_ENDPOINTS = {
  auth: '/api/auth',
  products: '/api/products',
  orders: '/api/orders',
  locations: '/api/locations',
  newsletter: '/api/newsletter',
  contact: '/api/contact',
  reviews: '/api/reviews',
  inventory: '/api/inventory',
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  ageVerified: 'apothecary_age_verified',
  selectedLocation: 'apothecary_selected_location',
  cart: 'apothecary_cart',
  preferences: 'apothecary_preferences',
  recentSearches: 'apothecary_recent_searches',
  favorites: 'apothecary_favorites',
} as const;

// Cookie Names
export const COOKIE_NAMES = {
  ageVerified: 'age_verified',
  location: 'selected_location',
  session: 'session_token',
  preferences: 'user_preferences',
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  ageVerification: 'You must be 21 or older to access this site.',
  locationRequired: 'Please select a location to continue shopping.',
  networkError: 'Network error. Please check your connection and try again.',
  serverError: 'Server error. Please try again later.',
  invalidInput: 'Please check your input and try again.',
  unauthorized: 'You are not authorized to perform this action.',
  notFound: 'The requested resource was not found.',
  cartEmpty: 'Your cart is empty.',
  inventoryUnavailable: 'This item is currently out of stock.',
  purchaseLimitExceeded: 'Purchase limit exceeded for this product.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  newsletterSubscribed: 'Successfully subscribed to our newsletter!',
  orderPlaced: 'Your order has been placed successfully!',
  profileUpdated: 'Your profile has been updated.',
  reviewSubmitted: 'Thank you for your review!',
  contactFormSubmitted: 'Your message has been sent successfully!',
  itemAddedToCart: 'Item added to cart.',
  itemRemovedFromCart: 'Item removed from cart.',
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  email: {
    required: 'Email is required',
    invalid: 'Please enter a valid email address',
  },
  phone: {
    required: 'Phone number is required',
    invalid: 'Please enter a valid phone number',
  },
  password: {
    required: 'Password is required',
    minLength: 'Password must be at least 8 characters',
    pattern: 'Password must contain at least one uppercase letter, one lowercase letter, and one number',
  },
  name: {
    required: 'Name is required',
    minLength: 'Name must be at least 2 characters',
  },
  age: {
    required: 'Age verification is required',
    minimum: 'You must be at least 21 years old',
  },
} as const;

// Feature Flags
export const FEATURE_FLAGS = {
  enableDelivery: process.env.NEXT_PUBLIC_ENABLE_DELIVERY === 'true',
  enableLoyaltyProgram: process.env.NEXT_PUBLIC_ENABLE_LOYALTY === 'true',
  enableReviews: process.env.NEXT_PUBLIC_ENABLE_REVIEWS === 'true',
  enableChat: process.env.NEXT_PUBLIC_ENABLE_CHAT === 'true',
  enableNotifications: process.env.NEXT_PUBLIC_ENABLE_NOTIFICATIONS === 'true',
} as const;

// Animation Durations (in milliseconds)
export const ANIMATION_DURATIONS = {
  fast: 150,
  normal: 300,
  slow: 500,
  verySlow: 1000,
} as const;

// Breakpoints (in pixels)
export const BREAKPOINTS = {
  xs: 320,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

// Z-Index Scale
export const Z_INDEX = {
  dropdown: 1000,
  sticky: 1020,
  fixed: 1030,
  modalBackdrop: 1040,
  modal: 1050,
  popover: 1060,
  tooltip: 1070,
  toast: 1080,
} as const;
