import React from 'react';
import { cn } from '@/lib/utils';
import { Container } from '../Container';

export interface SectionProps extends React.HTMLAttributes<HTMLElement> {
  background?: 'white' | 'cream' | 'stone' | 'sage' | 'image';
  backgroundImage?: string;
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  containerMaxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  overlay?: boolean;
  overlayOpacity?: 'light' | 'medium' | 'dark';
  children: React.ReactNode;
}

const backgroundVariants = {
  white: 'bg-neutral-white',
  cream: 'bg-neutral-cream',
  stone: 'bg-neutral-stone',
  sage: 'bg-primary-sage text-neutral-white',
  image: 'bg-cover bg-center bg-no-repeat relative',
} as const;

const paddingVariants = {
  none: '',
  sm: 'py-8',
  md: 'py-12',
  lg: 'py-16',
  xl: 'py-24',
} as const;

const overlayVariants = {
  light: 'bg-black/20',
  medium: 'bg-black/40',
  dark: 'bg-black/60',
} as const;

export const Section: React.FC<SectionProps> = ({
  background = 'white',
  backgroundImage,
  padding = 'md',
  containerMaxWidth = 'xl',
  overlay = false,
  overlayOpacity = 'medium',
  className,
  children,
  style,
  ...props
}) => {
  const sectionStyle = {
    ...style,
    ...(background === 'image' && backgroundImage && {
      backgroundImage: `url(${backgroundImage})`,
    }),
  };

  return (
    <section
      className={cn(
        backgroundVariants[background],
        paddingVariants[padding],
        className
      )}
      style={sectionStyle}
      {...props}
    >
      {/* Overlay for background images */}
      {background === 'image' && overlay && (
        <div className={cn('absolute inset-0', overlayVariants[overlayOpacity])} />
      )}
      
      {/* Content container */}
      <Container 
        maxWidth={containerMaxWidth}
        className={background === 'image' ? 'relative z-10' : undefined}
      >
        {children}
      </Container>
    </section>
  );
};

export default Section;
