<svg width="1920" height="1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="heroGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#87A96B;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#4A5D23;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#A8C090;stop-opacity:0.7" />
    </linearGradient>
    <pattern id="botanicalPattern" x="0" y="0" width="200" height="200" patternUnits="userSpaceOnUse">
      <circle cx="50" cy="50" r="2" fill="#87A96B" opacity="0.3"/>
      <circle cx="150" cy="150" r="2" fill="#A8C090" opacity="0.3"/>
      <path d="M100,80 Q120,60 140,80 Q120,100 100,80" fill="#4A5D23" opacity="0.2"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#heroGradient)"/>
  
  <!-- Pattern overlay -->
  <rect width="100%" height="100%" fill="url(#botanicalPattern)"/>
  
  <!-- Botanical elements -->
  <g opacity="0.4">
    <!-- Leaves -->
    <path d="M200,300 Q250,250 300,300 Q250,350 200,300" fill="#87A96B"/>
    <path d="M1600,200 Q1650,150 1700,200 Q1650,250 1600,200" fill="#A8C090"/>
    <path d="M300,800 Q350,750 400,800 Q350,850 300,800" fill="#4A5D23"/>
    
    <!-- Stems -->
    <line x1="250" y1="300" x2="250" y2="500" stroke="#4A5D23" stroke-width="3"/>
    <line x1="1650" y1="200" x2="1650" y2="400" stroke="#87A96B" stroke-width="3"/>
    <line x1="350" y1="800" x2="350" y2="1000" stroke="#A8C090" stroke-width="3"/>
  </g>
  
  <!-- Text overlay area (darker for readability) -->
  <rect x="0" y="400" width="100%" height="280" fill="rgba(26,26,26,0.3)"/>
  
  <!-- Subtle texture -->
  <rect width="100%" height="100%" fill="url(data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E)"/>
</svg>
