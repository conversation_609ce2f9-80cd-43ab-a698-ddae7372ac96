import { Metadata } from 'next';
import { Hero } from '@/components/content/Hero';
import { ProductGrid } from '@/components/content/ProductGrid';
import { Section } from '@/components/layout/Section';
import { Card, CardContent } from '@/components/ui/Card';
import { Icon } from '@/components/ui/Icon';
import { productCategories } from '@/data/products';

export const metadata: Metadata = {
  title: 'Shop Cannabis Products | Apothecary Extracts',
  description: 'Browse our premium selection of cannabis flower, concentrates, edibles, tinctures, and more. High-quality products with lab testing and expert guidance.',
  keywords: 'cannabis products, marijuana dispensary, flower, concentrates, edibles, tinctures, Boston cannabis, Cambridge marijuana',
};

export default function ShopPage() {
  return (
    <>
      {/* Hero Section */}
      <Hero
        subtitle="Premium Cannabis Products"
        title="Discover Our Curated Selection"
        description="From traditional flower to innovative extracts, explore our carefully selected cannabis products that meet the highest standards of quality and purity."
        backgroundImage="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80"
        primaryCTA={{
          text: "Browse All Products",
          href: "#products",
          icon: "leaf"
        }}
        secondaryCTA={{
          text: "Find Location",
          href: "/locations",
          icon: "map-pin"
        }}
      />

      {/* Location Notice */}
      <Section padding="md" background="cream">
        <div className="max-w-4xl mx-auto">
          <Card className="border-l-4 border-l-primary-sage">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <Icon name="info" size="md" className="text-primary-sage mt-1 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-neutral-charcoal mb-1">
                    Select Your Location
                  </h3>
                  <p className="text-sm text-neutral-charcoal/70">
                    Product availability and pricing may vary by location. Please select your preferred 
                    dispensary to view accurate inventory and place orders.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </Section>

      {/* Product Categories */}
      <Section padding="xl" background="white" id="products">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-charcoal mb-4 font-accent">
              Product Categories
            </h2>
            <p className="text-lg text-neutral-charcoal/70 max-w-2xl mx-auto">
              Explore our comprehensive selection of premium cannabis products, 
              each carefully tested and selected for quality and effectiveness.
            </p>
          </div>
          
          <ProductGrid 
            products={productCategories}
            columns={3}
            gap="lg"
            showAll={true}
          />
        </div>
      </Section>

      {/* Quality Assurance */}
      <Section padding="xl" background="sage">
        <div className="max-w-4xl mx-auto text-center text-neutral-white">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 font-accent">
            Quality You Can Trust
          </h2>
          <p className="text-xl text-neutral-white/90 mb-12">
            Every product in our dispensary undergoes rigorous testing and quality control 
            to ensure safety, potency, and purity.
          </p>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-neutral-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon name="beaker" size="xl" className="text-neutral-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Lab Tested</h3>
              <p className="text-neutral-white/80 text-sm">
                Third-party testing for potency, pesticides, and contaminants
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-neutral-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon name="shield" size="xl" className="text-neutral-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Quality Assured</h3>
              <p className="text-neutral-white/80 text-sm">
                Strict quality control standards from cultivation to sale
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-neutral-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Icon name="award" size="xl" className="text-neutral-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Expert Curated</h3>
              <p className="text-neutral-white/80 text-sm">
                Selected by our team of cannabis professionals and pharmacists
              </p>
            </div>
          </div>
        </div>
      </Section>

      {/* Shopping Information */}
      <Section padding="xl" background="white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-charcoal mb-4 font-accent">
              Shopping Information
            </h2>
            <p className="text-lg text-neutral-charcoal/70">
              Everything you need to know about shopping at Apothecary Extracts
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="p-6">
              <CardContent className="p-0">
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Icon name="user" size="lg" className="text-primary-sage" />
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-charcoal mb-2">Age Requirements</h3>
                  <p className="text-neutral-charcoal/70 text-sm">
                    21+ for recreational, 18+ for medical patients with valid card
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="p-0">
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Icon name="cart" size="lg" className="text-primary-sage" />
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-charcoal mb-2">Purchase Limits</h3>
                  <p className="text-neutral-charcoal/70 text-sm">
                    1 oz flower, 5g concentrates per day for recreational customers
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="p-0">
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Icon name="clock" size="lg" className="text-primary-sage" />
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-charcoal mb-2">Pre-Orders</h3>
                  <p className="text-neutral-charcoal/70 text-sm">
                    Order online for pickup, held for 24 hours
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="p-0">
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Icon name="award" size="lg" className="text-primary-sage" />
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-charcoal mb-2">Loyalty Program</h3>
                  <p className="text-neutral-charcoal/70 text-sm">
                    Earn points on every purchase, exclusive member benefits
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="p-0">
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Icon name="heart" size="lg" className="text-primary-sage" />
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-charcoal mb-2">Consultations</h3>
                  <p className="text-neutral-charcoal/70 text-sm">
                    Free personalized consultations with our cannabis experts
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="p-0">
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Icon name="shield" size="lg" className="text-primary-sage" />
                  </div>
                  <h3 className="text-lg font-semibold text-neutral-charcoal mb-2">Safe & Secure</h3>
                  <p className="text-neutral-charcoal/70 text-sm">
                    Secure facility with professional, knowledgeable staff
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Section>

      {/* CTA Section */}
      <Section padding="xl" background="cream" className="text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-neutral-charcoal mb-6 font-accent">
            Ready to Shop?
          </h2>
          <p className="text-lg text-neutral-charcoal/70 mb-8">
            Visit one of our locations or browse our online menu to start your order.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/locations"
              className="inline-flex items-center px-8 py-3 text-lg font-medium text-neutral-white bg-primary-sage rounded-md hover:bg-primary-forest transition-colors duration-200"
            >
              Find a Location
            </a>
            <a
              href="/contact"
              className="inline-flex items-center px-8 py-3 text-lg font-medium text-primary-sage border border-primary-sage rounded-md hover:bg-primary-sage hover:text-neutral-white transition-colors duration-200"
            >
              Ask Questions
            </a>
          </div>
        </div>
      </Section>
    </>
  );
}
