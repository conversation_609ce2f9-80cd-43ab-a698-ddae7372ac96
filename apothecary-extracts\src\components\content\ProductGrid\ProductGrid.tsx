import React from 'react';
import { cn } from '@/lib/utils';
import { ProductCard } from './ProductCard';
import type { ProductCategoryData } from '@/data/products';

export interface ProductGridProps {
  products: ProductCategoryData[];
  columns?: 2 | 3 | 4 | 6;
  gap?: 'sm' | 'md' | 'lg';
  showAll?: boolean;
  className?: string;
}

const columnClasses = {
  2: 'grid-cols-1 sm:grid-cols-2',
  3: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3',
  4: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-4',
  6: 'grid-cols-2 sm:grid-cols-3 lg:grid-cols-6',
} as const;

const gapClasses = {
  sm: 'gap-4',
  md: 'gap-6',
  lg: 'gap-8',
} as const;

export const ProductGrid: React.FC<ProductGridProps> = ({
  products,
  columns = 3,
  gap = 'md',
  showAll = false,
  className,
}) => {
  const displayProducts = showAll ? products : products.filter(p => p.featured);

  return (
    <div className={cn('w-full', className)}>
      <div className={cn(
        'grid',
        columnClasses[columns],
        gapClasses[gap]
      )}>
        {displayProducts.map((product) => (
          <ProductCard
            key={product.id}
            product={product}
          />
        ))}
      </div>
      
      {!showAll && products.length > displayProducts.length && (
        <div className="mt-8 text-center">
          <a
            href="/shop"
            className="inline-flex items-center px-6 py-3 text-base font-medium text-primary-sage border border-primary-sage rounded-md hover:bg-primary-sage hover:text-neutral-white transition-colors duration-200"
          >
            View All Products
          </a>
        </div>
      )}
    </div>
  );
};

export default ProductGrid;
