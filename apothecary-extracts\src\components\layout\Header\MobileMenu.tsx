'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Icon } from '@/components/ui/Icon';
import { Button } from '@/components/ui/Button';
import { mainNavigation } from '@/data/navigation';

export interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

export const MobileMenu: React.FC<MobileMenuProps> = ({ isOpen, onClose }) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const toggleExpanded = (label: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(label)) {
      newExpanded.delete(label);
    } else {
      newExpanded.add(label);
    }
    setExpandedItems(newExpanded);
  };

  const handleLinkClick = () => {
    onClose();
    setExpandedItems(new Set());
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 z-40 lg:hidden"
        onClick={onClose}
      />

      {/* Mobile Menu */}
      <div className="fixed top-0 right-0 h-full w-80 max-w-[90vw] bg-neutral-white shadow-xl z-50 lg:hidden transform transition-transform duration-300">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-neutral-stone">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary-sage rounded-full flex items-center justify-center">
              <Icon name="leaf" size="sm" className="text-neutral-white" />
            </div>
            <span className="text-lg font-bold text-primary-sage font-accent">
              Apothecary Extracts
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="p-2"
          >
            <Icon name="close" size="md" />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto">
          <div className="py-4">
            {mainNavigation.map((item) => (
              <div key={item.label}>
                {/* Main Item */}
                <div className="flex items-center">
                  <Link
                    href={item.href}
                    onClick={handleLinkClick}
                    className="flex-1 px-4 py-3 text-base font-medium text-neutral-charcoal hover:bg-neutral-cream hover:text-primary-sage transition-colors duration-200"
                  >
                    {item.label}
                  </Link>
                  
                  {/* Expand Button */}
                  {item.children && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleExpanded(item.label)}
                      className="mr-2 p-2"
                    >
                      <Icon 
                        name={expandedItems.has(item.label) ? 'chevron-up' : 'chevron-down'} 
                        size="sm" 
                      />
                    </Button>
                  )}
                </div>

                {/* Submenu */}
                {item.children && expandedItems.has(item.label) && (
                  <div className="bg-neutral-cream/50">
                    {item.children.map((child) => (
                      <Link
                        key={child.label}
                        href={child.href}
                        onClick={handleLinkClick}
                        className="block px-8 py-3 text-sm text-neutral-charcoal hover:bg-neutral-cream hover:text-primary-sage transition-colors duration-200"
                      >
                        <div className="font-medium">{child.label}</div>
                        {child.description && (
                          <div className="text-xs text-neutral-charcoal/60 mt-1">
                            {child.description}
                          </div>
                        )}
                      </Link>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-neutral-stone">
          <div className="space-y-2">
            <Button
              variant="primary"
              fullWidth
              onClick={handleLinkClick}
            >
              Shop Now
            </Button>
            <Button
              variant="ghost"
              fullWidth
              onClick={handleLinkClick}
            >
              Find Locations
            </Button>
          </div>
        </div>
      </div>
    </>
  );
};

export default MobileMenu;
