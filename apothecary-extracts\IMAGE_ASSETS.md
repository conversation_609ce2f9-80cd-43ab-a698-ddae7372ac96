# Apothecary Extracts - Image Assets Reference

## Overview
This document tracks all image assets used in the Apothecary Extracts website, including their sources, dimensions, and usage.

## Hero Background Images (1920x1080)

### Homepage Hero
- **File**: `/images/hero/hero-bg.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80`
- **Description**: Modern botanical/medicinal aesthetic with natural lighting
- **Usage**: Homepage hero background
- **Alt Text**: "Premium medicinal cannabis dispensary interior"

### About Page Hero
- **File**: `/images/hero/about-hero.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1576086213369-97a306d36557?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80`
- **Description**: Traditional apothecary meets modern science aesthetic
- **Usage**: About page hero background
- **Alt Text**: "Traditional apothecary laboratory with modern equipment"

### Shop Page Hero
- **File**: `/images/hero/shop-hero.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80`
- **Description**: Premium cannabis products display
- **Usage**: Shop page hero background
- **Alt Text**: "Premium cannabis products showcase"

### Locations Page Hero
- **File**: `/images/hero/locations-hero.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80`
- **Description**: Professional dispensary storefront
- **Usage**: Locations page hero background
- **Alt Text**: "Modern cannabis dispensary storefront"

## Product Category Images (400x400)

### Premium Flower
- **File**: `/images/products/flower.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop&crop=center&auto=format&q=80`
- **Description**: High-quality cannabis flower buds
- **Usage**: Flower product category card
- **Alt Text**: "Premium cannabis flower buds"

### Pre-rolls
- **File**: `/images/products/pre-rolls.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=400&fit=crop&crop=center&auto=format&q=80`
- **Description**: Professionally rolled cannabis joints
- **Usage**: Pre-rolls product category card
- **Alt Text**: "Cannabis pre-rolls and joints"

### Concentrates
- **File**: `/images/products/concentrates.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=400&h=400&fit=crop&crop=center&auto=format&q=80`
- **Description**: Cannabis concentrates and extracts
- **Usage**: Concentrates product category card
- **Alt Text**: "Cannabis concentrates and extracts"

### Edibles
- **File**: `/images/products/edibles.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1499636136210-6f4ee915583e?w=400&h=400&fit=crop&crop=center&auto=format&q=80`
- **Description**: Cannabis-infused edibles and treats
- **Usage**: Edibles product category card
- **Alt Text**: "Cannabis edibles and infused treats"

### Tinctures
- **File**: `/images/products/tinctures.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=400&h=400&fit=crop&crop=center&auto=format&q=80`
- **Description**: Cannabis tinctures and oils
- **Usage**: Tinctures product category card
- **Alt Text**: "Cannabis tinctures and oils"

### Topicals
- **File**: `/images/products/topicals.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1556228720-195a672e8a03?w=400&h=400&fit=crop&crop=center&auto=format&q=80`
- **Description**: Cannabis topical products and skincare
- **Usage**: Topicals product category card
- **Alt Text**: "Cannabis topical products and balms"

### Vaporizers
- **File**: `/images/products/vaporizers.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1544966503-7cc5ac882d5f?w=400&h=400&fit=crop&crop=center&auto=format&q=80`
- **Description**: Cannabis vaporizers and cartridges
- **Usage**: Vaporizers product category card
- **Alt Text**: "Cannabis vaporizers and cartridges"

### Accessories
- **File**: `/images/products/accessories.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1574263867128-a3d5c1b1deaa?w=400&h=400&fit=crop&crop=center&auto=format&q=80`
- **Description**: Cannabis accessories and tools
- **Usage**: Accessories product category card
- **Alt Text**: "Cannabis accessories and tools"

## Location Images (800x600)

### Downtown Boston
- **File**: `/images/locations/downtown-boston.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop&crop=center&auto=format&q=80`
- **Description**: Downtown Boston dispensary location
- **Usage**: Downtown Boston location card
- **Alt Text**: "Apothecary Extracts Downtown Boston location"

### Cambridge
- **File**: `/images/locations/cambridge.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1556228720-195a672e8a03?w=800&h=600&fit=crop&crop=center&auto=format&q=80`
- **Description**: Cambridge dispensary location
- **Usage**: Cambridge location card
- **Alt Text**: "Apothecary Extracts Cambridge location"

## Social Media Images (1200x630)

### Open Graph Image
- **File**: `/images/og-image.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=1200&h=630&fit=crop&crop=center&auto=format&q=80`
- **Description**: Social media sharing image for Open Graph
- **Usage**: Facebook, LinkedIn, and other social media shares
- **Alt Text**: "Apothecary Extracts - Premium Cannabis Dispensary"

### Twitter Card Image
- **File**: `/images/twitter-image.jpg`
- **Current URL**: `https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=1200&h=630&fit=crop&crop=center&auto=format&q=80`
- **Description**: Social media sharing image for Twitter
- **Usage**: Twitter card shares
- **Alt Text**: "Apothecary Extracts - Premium Cannabis Dispensary"

## Image Optimization Settings

All images use Unsplash's URL parameters for optimization:
- `w=` - Width in pixels
- `h=` - Height in pixels  
- `fit=crop` - Crop to exact dimensions
- `crop=center` - Center crop positioning
- `auto=format` - Automatic format selection (WebP when supported)
- `q=80` - Quality setting (80% for good balance of quality/size)

## Next.js Configuration

The `next.config.ts` file includes remote pattern configuration for Unsplash:

```typescript
images: {
  remotePatterns: [
    {
      protocol: 'https',
      hostname: 'images.unsplash.com',
      port: '',
      pathname: '/**',
    },
  ],
}
```

## Future Improvements

1. **Custom Photography**: Replace stock images with custom photography of actual products and locations
2. **Brand Consistency**: Ensure all images align with the sage green/amber color palette
3. **Performance**: Consider using a CDN for faster image delivery
4. **Accessibility**: Add comprehensive alt text for all images
5. **Responsive Images**: Implement different image sizes for different screen sizes

## Notes

- All current images are sourced from Unsplash for placeholder purposes
- Images should be replaced with actual product photography and location photos
- Ensure compliance with cannabis advertising regulations in your jurisdiction
- Consider licensing requirements for commercial use of stock photography
