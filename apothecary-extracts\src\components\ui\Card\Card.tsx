import React from 'react';
import { cn } from '@/lib/utils';

export interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'product' | 'location' | 'testimonial' | 'elevated';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  hover?: boolean;
  children: React.ReactNode;
}

const cardVariants = {
  default: 'bg-neutral-white border border-neutral-stone shadow-sm',
  product: 'bg-neutral-white border border-neutral-stone shadow-sm hover:shadow-md',
  location: 'bg-neutral-white border border-neutral-stone shadow-md',
  testimonial: 'bg-neutral-cream border border-neutral-stone shadow-sm',
  elevated: 'bg-neutral-white border border-neutral-stone shadow-lg',
} as const;

const cardPadding = {
  none: '',
  sm: 'p-4',
  md: 'p-6',
  lg: 'p-8',
} as const;

export const Card: React.FC<CardProps> = ({
  variant = 'default',
  padding = 'md',
  hover = false,
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={cn(
        // Base styles
        'rounded-lg transition-all duration-200',
        
        // Variant styles
        cardVariants[variant],
        
        // Padding
        cardPadding[padding],
        
        // Hover effects
        hover && 'hover:shadow-md hover:-translate-y-1 cursor-pointer',
        
        // Custom className
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

// Card sub-components for better composition
export const CardHeader: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({
  className,
  children,
  ...props
}) => (
  <div
    className={cn('flex flex-col space-y-1.5', className)}
    {...props}
  >
    {children}
  </div>
);

export const CardTitle: React.FC<React.HTMLAttributes<HTMLHeadingElement>> = ({
  className,
  children,
  ...props
}) => (
  <h3
    className={cn('text-lg font-semibold leading-none tracking-tight text-neutral-charcoal', className)}
    {...props}
  >
    {children}
  </h3>
);

export const CardDescription: React.FC<React.HTMLAttributes<HTMLParagraphElement>> = ({
  className,
  children,
  ...props
}) => (
  <p
    className={cn('text-sm text-neutral-charcoal/70', className)}
    {...props}
  >
    {children}
  </p>
);

export const CardContent: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({
  className,
  children,
  ...props
}) => (
  <div
    className={cn('pt-0', className)}
    {...props}
  >
    {children}
  </div>
);

export const CardFooter: React.FC<React.HTMLAttributes<HTMLDivElement>> = ({
  className,
  children,
  ...props
}) => (
  <div
    className={cn('flex items-center pt-0', className)}
    {...props}
  >
    {children}
  </div>
);

export default Card;
