import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { STORAGE_KEYS } from '@/lib/constants';
import type { Location } from '@/lib/types';

interface LocationState {
  selectedLocation: Location | null;
  availableLocations: Location[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setSelectedLocation: (location: Location) => void;
  setAvailableLocations: (locations: Location[]) => void;
  clearSelectedLocation: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  getLocationById: (id: string) => Location | undefined;
}

export const useLocationStore = create<LocationState>()(
  persist(
    (set, get) => ({
      selectedLocation: null,
      availableLocations: [],
      isLoading: false,
      error: null,

      setSelectedLocation: (location: Location) => {
        set({ selectedLocation: location, error: null });
      },

      setAvailableLocations: (locations: Location[]) => {
        set({ availableLocations: locations });
      },

      clearSelectedLocation: () => {
        set({ selectedLocation: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      getLocationById: (id: string) => {
        const { availableLocations } = get();
        return availableLocations.find(location => location.id === id);
      },
    }),
    {
      name: STORAGE_KEYS.selectedLocation,
      partialize: (state) => ({
        selectedLocation: state.selectedLocation,
      }),
    }
  )
);
