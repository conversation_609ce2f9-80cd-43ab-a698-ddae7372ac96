<svg width="1920" height="1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="locationsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#B8956A;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#87A96B;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#D4A574;stop-opacity:0.7" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#locationsGradient)"/>
  
  <!-- Location elements -->
  <g opacity="0.5">
    <!-- Building silhouettes -->
    <rect x="300" y="200" width="80" height="200" fill="#4A5D23"/>
    <rect x="320" y="180" width="40" height="20" fill="#87A96B"/>
    <rect x="310" y="220" width="20" height="30" fill="#A8C090"/>
    <rect x="350" y="220" width="20" height="30" fill="#A8C090"/>
    
    <rect x="1400" y="250" width="100" height="180" fill="#87A96B"/>
    <rect x="1420" y="230" width="60" height="20" fill="#D4A574"/>
    <rect x="1410" y="270" width="25" height="35" fill="#B8956A"/>
    <rect x="1465" y="270" width="25" height="35" fill="#B8956A"/>
    
    <!-- Street elements -->
    <rect x="0" y="600" width="1920" height="40" fill="#4A5D23" opacity="0.3"/>
    <rect x="100" y="610" width="60" height="20" fill="#87A96B" opacity="0.5"/>
    <rect x="1700" y="610" width="60" height="20" fill="#A8C090" opacity="0.5"/>
  </g>
  
  <!-- Text overlay area -->
  <rect x="0" y="400" width="100%" height="280" fill="rgba(26,26,26,0.3)"/>
</svg>
