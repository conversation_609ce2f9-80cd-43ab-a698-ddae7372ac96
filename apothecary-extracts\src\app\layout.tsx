import type { Metadata, Viewport } from "next";
import "./globals.css";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { AgeGate } from "@/components/specialized/AgeVerification";

export const metadata: Metadata = {
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),
  title: "Apothecary Farms | Colorado's Premier Concentrate-Driven Dispensary",
  description: "Award-winning cannabis concentrates from Colorado's premier concentrate-driven dispensary. Featuring Apothecary Extracts™ live resin, live rosin, shatter. Locations in Colorado and Oklahoma.",
  keywords: "cannabis concentrates, dispensary, live resin, live rosin, shatter, Colorado dispensary, Oklahoma dispensary, Apothecary Extracts, medical marijuana, recreational cannabis",
  authors: [{ name: "Apothecary Farms" }],
  creator: "Apothecary Farms",
  publisher: "Apothecary Farms",
  robots: "index, follow",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://apothecaryfarms.com",
    siteName: "Apothecary Farms",
    title: "Apothecary Farms | Colorado's Premier Concentrate-Driven Dispensary",
    description: "Award-winning cannabis concentrates from Colorado's premier concentrate-driven dispensary. Featuring Apothecary Extracts™ live resin, live rosin, shatter.",
    images: [
      {
        url: "https://images.unsplash.com/photo-**********-c3190ca9959b?w=1200&h=630&fit=crop&crop=center&auto=format&q=80",
        width: 1200,
        height: 630,
        alt: "Apothecary Farms - Premium Cannabis Concentrates",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    site: "@apothecaryfarms",
    creator: "@apothecaryfarms",
    title: "Apothecary Farms | Colorado's Premier Concentrate-Driven Dispensary",
    description: "Award-winning cannabis concentrates from Colorado's premier concentrate-driven dispensary. Featuring Apothecary Extracts™ live resin, live rosin, shatter.",
    images: ["https://images.unsplash.com/photo-**********-c3190ca9959b?w=1200&h=630&fit=crop&crop=center&auto=format&q=80"],
  },
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#87A96B',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className="font-secondary antialiased">
        <Header />
        <main className="min-h-screen">
          {children}
        </main>
        <Footer />
        <AgeGate />
      </body>
    </html>
  );
}
