<svg width="1920" height="1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="shopGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#A8C090;stop-opacity:0.8" />
      <stop offset="50%" style="stop-color:#87A96B;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#4A5D23;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#shopGradient)"/>
  
  <!-- Product display elements -->
  <g opacity="0.4">
    <!-- Shelving -->
    <rect x="200" y="300" width="300" height="20" fill="#4A5D23"/>
    <rect x="200" y="500" width="300" height="20" fill="#4A5D23"/>
    <rect x="1400" y="250" width="300" height="20" fill="#87A96B"/>
    <rect x="1400" y="450" width="300" height="20" fill="#87A96B"/>
    
    <!-- Product containers -->
    <circle cx="300" cy="280" r="15" fill="#D4A574"/>
    <circle cx="400" cy="280" r="15" fill="#B8956A"/>
    <rect x="280" y="480" width="30" height="40" rx="5" fill="#87A96B"/>
    <rect x="380" y="480" width="30" height="40" rx="5" fill="#A8C090"/>
    
    <circle cx="1500" cy="230" r="15" fill="#87A96B"/>
    <circle cx="1600" cy="230" r="15" fill="#A8C090"/>
    <rect x="1480" y="430" width="30" height="40" rx="5" fill="#D4A574"/>
    <rect x="1580" y="430" width="30" height="40" rx="5" fill="#B8956A"/>
  </g>
  
  <!-- Text overlay area -->
  <rect x="0" y="400" width="100%" height="280" fill="rgba(26,26,26,0.3)"/>
</svg>
