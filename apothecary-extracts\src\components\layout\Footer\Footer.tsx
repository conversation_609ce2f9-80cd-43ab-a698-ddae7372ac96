import React from 'react';
import Link from 'next/link';
import { Container } from '../Container';
import { Icon } from '@/components/ui/Icon';
import { SocialLinksFooter } from '@/components/specialized/SocialLinks';
import { AppDownload } from '@/components/specialized/AppDownload';
import { NewsletterForm } from '@/components/forms/NewsletterForm';
import { footerNavigation } from '@/data/navigation';
import { CONTACT_INFO, APP_CONFIG } from '@/lib/constants';

export interface FooterProps {
  showNewsletter?: boolean;
  showAppDownload?: boolean;
  className?: string;
}

export const Footer: React.FC<FooterProps> = ({
  showNewsletter = true,
  showAppDownload = true,
  className,
}) => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={`bg-neutral-charcoal text-neutral-white ${className}`}>
      {/* Newsletter Section */}
      {showNewsletter && (
        <div className="bg-primary-sage">
          <Container>
            <div className="py-12">
              <NewsletterForm
                title="Stay Connected with Apothecary Extracts"
                description="Get exclusive deals, new product announcements, and cannabis education delivered to your inbox."
                variant="inline"
                className="text-neutral-white"
              />
            </div>
          </Container>
        </div>
      )}

      {/* Main Footer Content */}
      <Container>
        <div className="py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="lg:col-span-1">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-primary-sage rounded-full flex items-center justify-center">
                  <Icon name="leaf" size="sm" className="text-neutral-white" />
                </div>
                <span className="text-xl font-bold font-accent">
                  Apothecary Extracts
                </span>
              </div>
              <p className="text-neutral-white/70 mb-6 text-sm leading-relaxed">
                Premium medicinal cannabis dispensary specializing in artisanal extracts 
                and healing botanicals. Where traditional apothecary meets modern cannabis science.
              </p>
              <SocialLinksFooter />
            </div>

            {/* Products */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Products</h3>
              <ul className="space-y-2">
                {footerNavigation.products.map((item) => (
                  <li key={item.label}>
                    <Link
                      href={item.href}
                      className="text-neutral-white/70 hover:text-neutral-white transition-colors duration-200 text-sm"
                    >
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Company</h3>
              <ul className="space-y-2">
                {footerNavigation.company.map((item) => (
                  <li key={item.label}>
                    <Link
                      href={item.href}
                      className="text-neutral-white/70 hover:text-neutral-white transition-colors duration-200 text-sm"
                    >
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Support */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Support</h3>
              <ul className="space-y-2">
                {footerNavigation.support.map((item) => (
                  <li key={item.label}>
                    <Link
                      href={item.href}
                      className="text-neutral-white/70 hover:text-neutral-white transition-colors duration-200 text-sm"
                    >
                      {item.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* App Download Section */}
          {showAppDownload && (
            <div className="mt-12 pt-8 border-t border-neutral-white/20">
              <AppDownload
                variant="inline"
                className="text-neutral-white"
              />
            </div>
          )}

          {/* Contact Info */}
          <div className="mt-12 pt-8 border-t border-neutral-white/20">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
              <div className="flex items-center space-x-2">
                <Icon name="phone" size="sm" className="text-primary-sage" />
                <span className="text-neutral-white/70">{CONTACT_INFO.phone}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Icon name="mail" size="sm" className="text-primary-sage" />
                <span className="text-neutral-white/70">{CONTACT_INFO.email}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Icon name="clock" size="sm" className="text-primary-sage" />
                <span className="text-neutral-white/70">
                  {CONTACT_INFO.businessHours.weekdays}
                </span>
              </div>
            </div>
          </div>
        </div>
      </Container>

      {/* Bottom Bar */}
      <div className="bg-neutral-black">
        <Container>
          <div className="py-6 flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-neutral-white/60">
              © {currentYear} {APP_CONFIG.name}. All rights reserved.
            </div>
            
            <div className="flex flex-wrap justify-center md:justify-end gap-6 text-sm">
              {footerNavigation.legal.map((item) => (
                <Link
                  key={item.label}
                  href={item.href}
                  className="text-neutral-white/60 hover:text-neutral-white transition-colors duration-200"
                >
                  {item.label}
                </Link>
              ))}
            </div>
          </div>
        </Container>
      </div>

      {/* Legal Disclaimer */}
      <div className="bg-neutral-black border-t border-neutral-white/10">
        <Container>
          <div className="py-4">
            <p className="text-xs text-neutral-white/50 text-center leading-relaxed">
              This product has not been analyzed or approved by the FDA. There is limited information on the side effects of using this product, 
              and there may be associated health risks. Marijuana use during pregnancy and breast-feeding may pose potential harms. 
              It is against the law to drive or operate machinery when under the influence of this product. 
              KEEP THIS PRODUCT AWAY FROM CHILDREN. There may be health risks associated with consumption of this product. 
              Marijuana can impair concentration, coordination, and judgment. The impairment effects of edible marijuana may be delayed by two hours or more.
            </p>
          </div>
        </Container>
      </div>
    </footer>
  );
};

export default Footer;
