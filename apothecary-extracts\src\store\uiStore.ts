import { create } from 'zustand';

interface UIState {
  // Mobile menu
  isMobileMenuOpen: boolean;
  
  // Modals
  isAgeGateOpen: boolean;
  isLocationSelectorOpen: boolean;
  isCartOpen: boolean;
  isSearchOpen: boolean;
  
  // Loading states
  isLoading: boolean;
  loadingMessage: string | null;
  
  // Error states
  error: string | null;
  
  // Toast notifications
  toasts: Toast[];
  
  // Actions
  setMobileMenuOpen: (open: boolean) => void;
  toggleMobileMenu: () => void;
  
  setAgeGateOpen: (open: boolean) => void;
  setLocationSelectorOpen: (open: boolean) => void;
  setCartOpen: (open: boolean) => void;
  setSearchOpen: (open: boolean) => void;
  
  setLoading: (loading: boolean, message?: string) => void;
  setError: (error: string | null) => void;
  
  addToast: (toast: Omit<Toast, 'id'>) => void;
  removeToast: (id: string) => void;
  clearToasts: () => void;
  
  closeAllModals: () => void;
}

interface Toast {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export const useUIStore = create<UIState>((set, get) => ({
  // Initial state
  isMobileMenuOpen: false,
  isAgeGateOpen: false,
  isLocationSelectorOpen: false,
  isCartOpen: false,
  isSearchOpen: false,
  isLoading: false,
  loadingMessage: null,
  error: null,
  toasts: [],

  // Mobile menu actions
  setMobileMenuOpen: (open: boolean) => {
    set({ isMobileMenuOpen: open });
  },

  toggleMobileMenu: () => {
    set(state => ({ isMobileMenuOpen: !state.isMobileMenuOpen }));
  },

  // Modal actions
  setAgeGateOpen: (open: boolean) => {
    set({ isAgeGateOpen: open });
  },

  setLocationSelectorOpen: (open: boolean) => {
    set({ isLocationSelectorOpen: open });
  },

  setCartOpen: (open: boolean) => {
    set({ isCartOpen: open });
  },

  setSearchOpen: (open: boolean) => {
    set({ isSearchOpen: open });
  },

  // Loading actions
  setLoading: (loading: boolean, message?: string) => {
    set({ 
      isLoading: loading, 
      loadingMessage: loading ? message || null : null 
    });
  },

  // Error actions
  setError: (error: string | null) => {
    set({ error });
  },

  // Toast actions
  addToast: (toast: Omit<Toast, 'id'>) => {
    const id = `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newToast: Toast = {
      ...toast,
      id,
      duration: toast.duration || 5000,
    };

    set(state => ({
      toasts: [...state.toasts, newToast]
    }));

    // Auto-remove toast after duration
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        get().removeToast(id);
      }, newToast.duration);
    }
  },

  removeToast: (id: string) => {
    set(state => ({
      toasts: state.toasts.filter(toast => toast.id !== id)
    }));
  },

  clearToasts: () => {
    set({ toasts: [] });
  },

  // Utility actions
  closeAllModals: () => {
    set({
      isMobileMenuOpen: false,
      isAgeGateOpen: false,
      isLocationSelectorOpen: false,
      isCartOpen: false,
      isSearchOpen: false,
    });
  },
}));

// Toast helper functions
export const toast = {
  success: (title: string, message?: string, options?: Partial<Toast>) => {
    useUIStore.getState().addToast({
      type: 'success',
      title,
      message,
      ...options,
    });
  },

  error: (title: string, message?: string, options?: Partial<Toast>) => {
    useUIStore.getState().addToast({
      type: 'error',
      title,
      message,
      duration: 7000, // Longer duration for errors
      ...options,
    });
  },

  warning: (title: string, message?: string, options?: Partial<Toast>) => {
    useUIStore.getState().addToast({
      type: 'warning',
      title,
      message,
      ...options,
    });
  },

  info: (title: string, message?: string, options?: Partial<Toast>) => {
    useUIStore.getState().addToast({
      type: 'info',
      title,
      message,
      ...options,
    });
  },
};
