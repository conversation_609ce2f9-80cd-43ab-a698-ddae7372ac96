import React from 'react';
import { 
  Menu,
  X,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  Search,
  ShoppingCart,
  MapPin,
  Phone,
  Mail,
  Clock,
  Star,
  Heart,
  User,
  Settings,
  Home,
  Leaf,
  Beaker,
  Pill,
  Droplets,
  Zap,
  Shield,
  Award,
  Check,
  AlertCircle,
  Info,
  XCircle,
  Plus,
  Minus,
  Eye,
  EyeOff,
  Facebook,
  Instagram,
  Twitter,
  Youtube,
  Linkedin,
  ExternalLink,
  Download,
  Upload,
  Filter,
  SortAsc,
  SortDesc,
  Calendar,
  MessageCircle,
  Share2,
  Bookmark,
  Edit,
  Trash2,
  Copy,
  MoreHorizontal,
  MoreVertical,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  type LucideIcon,
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Icon mapping
const iconMap = {
  // Navigation
  menu: Menu,
  close: X,
  'chevron-down': ChevronDown,
  'chevron-up': ChevronUp,
  'chevron-left': ChevronLeft,
  'chevron-right': ChevronRight,
  'arrow-left': ArrowLeft,
  'arrow-right': ArrowRight,
  'arrow-up': ArrowUp,
  'arrow-down': ArrowDown,
  
  // Actions
  search: Search,
  cart: ShoppingCart,
  plus: Plus,
  minus: Minus,
  edit: Edit,
  trash: Trash2,
  copy: Copy,
  share: Share2,
  bookmark: Bookmark,
  download: Download,
  upload: Upload,
  'external-link': ExternalLink,
  
  // Interface
  eye: Eye,
  'eye-off': EyeOff,
  filter: Filter,
  'sort-asc': SortAsc,
  'sort-desc': SortDesc,
  'more-horizontal': MoreHorizontal,
  'more-vertical': MoreVertical,
  
  // Status
  check: Check,
  'alert-circle': AlertCircle,
  info: Info,
  'x-circle': XCircle,
  star: Star,
  heart: Heart,
  award: Award,
  shield: Shield,
  
  // Contact & Location
  'map-pin': MapPin,
  phone: Phone,
  mail: Mail,
  clock: Clock,
  calendar: Calendar,
  'message-circle': MessageCircle,
  
  // User
  user: User,
  settings: Settings,
  home: Home,
  
  // Cannabis/Medical
  leaf: Leaf,
  beaker: Beaker,
  pill: Pill,
  droplets: Droplets,
  zap: Zap,
  
  // Social Media
  facebook: Facebook,
  instagram: Instagram,
  twitter: Twitter,
  youtube: Youtube,
  linkedin: Linkedin,
} as const;

export type IconName = keyof typeof iconMap;

export interface IconProps {
  name: IconName;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  className?: string;
  color?: string;
  strokeWidth?: number;
}

const sizeMap = {
  xs: 'w-3 h-3',
  sm: 'w-4 h-4',
  md: 'w-5 h-5',
  lg: 'w-6 h-6',
  xl: 'w-8 h-8',
  '2xl': 'w-10 h-10',
} as const;

export const Icon: React.FC<IconProps> = ({
  name,
  size = 'md',
  className,
  color,
  strokeWidth = 2,
  ...props
}) => {
  const IconComponent = iconMap[name] as LucideIcon;
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found`);
    return null;
  }

  return (
    <IconComponent
      className={cn(sizeMap[size], className)}
      style={{ color }}
      strokeWidth={strokeWidth}
      {...props}
    />
  );
};

export default Icon;
