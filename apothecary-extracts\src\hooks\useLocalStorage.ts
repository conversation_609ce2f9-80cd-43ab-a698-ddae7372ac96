'use client';

import { useState, useEffect } from 'react';

type SetValue<T> = T | ((val: T) => T);

/**
 * Custom hook for localStorage with SSR support
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T
): [T, (value: SetValue<T>) => void] {
  // State to store our value
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === 'undefined') {
      return initialValue;
    }
    
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = (value: SetValue<T>) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // Save state
      setStoredValue(valueToStore);
      
      // Save to localStorage
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
}

/**
 * Hook to remove an item from localStorage
 */
export function useRemoveFromLocalStorage() {
  const removeItem = (key: string) => {
    try {
      if (typeof window !== 'undefined') {
        window.localStorage.removeItem(key);
      }
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error);
    }
  };

  return removeItem;
}

/**
 * Hook to clear all localStorage
 */
export function useClearLocalStorage() {
  const clearAll = () => {
    try {
      if (typeof window !== 'undefined') {
        window.localStorage.clear();
      }
    } catch (error) {
      console.warn('Error clearing localStorage:', error);
    }
  };

  return clearAll;
}

/**
 * Hook to get localStorage size
 */
export function useLocalStorageSize() {
  const [size, setSize] = useState(0);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const calculateSize = () => {
      let total = 0;
      for (let key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          total += localStorage[key].length + key.length;
        }
      }
      setSize(total);
    };

    calculateSize();

    // Listen for storage changes
    const handleStorageChange = () => {
      calculateSize();
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  return size;
}

/**
 * Hook to listen for localStorage changes
 */
export function useLocalStorageListener(
  key: string,
  callback: (newValue: any, oldValue: any) => void
) {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    let oldValue: any;
    try {
      const item = window.localStorage.getItem(key);
      oldValue = item ? JSON.parse(item) : null;
    } catch (error) {
      oldValue = null;
    }

    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key) {
        let newValue: any;
        try {
          newValue = e.newValue ? JSON.parse(e.newValue) : null;
        } catch (error) {
          newValue = e.newValue;
        }
        
        callback(newValue, oldValue);
        oldValue = newValue;
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key, callback]);
}
