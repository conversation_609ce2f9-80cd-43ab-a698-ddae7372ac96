# Apothecary Extracts - Cannabis Dispensary Website

## Overview
This repository contains a modern, responsive cannabis dispensary website for Apothecary Extracts, built with Next.js 14+, TypeScript, and Tailwind CSS. The project emphasizes medicinal cannabis, traditional apothecary heritage, and scientific precision in healing.

## 🎯 Project Objectives
- Create a premium medicinal cannabis dispensary website
- Implement comprehensive design system with botanical aesthetics
- Ensure cannabis industry compliance and accessibility
- Provide exceptional user experience for healing-focused customers
- Maintain high performance and SEO optimization

## 🌿 Features

### Cannabis Industry Compliance
- **Age Verification**: Mandatory age gate with localStorage persistence
- **Legal Disclaimers**: Comprehensive compliance messaging
- **Purchase Limits**: Recreational and medical purchase limit tracking
- **Location-Based Services**: Multi-dispensary support with location switching

### Modern Web Technologies
- **Next.js 14+**: App Router with TypeScript
- **Tailwind CSS v4**: Custom design system with medicinal color palette
- **Framer Motion**: Smooth animations and transitions
- **Zustand**: Lightweight state management
- **React Hook Form**: Form validation and handling

### User Experience
- **Responsive Design**: Mobile-first approach with touch-friendly interactions
- **Accessibility**: WCAG 2.2 AA compliance built-in
- **Performance**: Optimized images, lazy loading, and code splitting
- **SEO Optimized**: Proper metadata, Open Graph, and Twitter Cards

## 📋 Documentation

### ✅ Complete Documentation Set
1. **[DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md)** - Apothecary Extracts design system with botanical aesthetics
2. **[COMPONENT_ARCHITECTURE.md](./COMPONENT_ARCHITECTURE.md)** - Complete mapping of reusable UI components with TypeScript interfaces
3. **[PROJECT_STRUCTURE.md](./PROJECT_STRUCTURE.md)** - React project scaffold with organized file and folder structure
4. **[PAGE_LAYOUT_ANALYSIS.md](./PAGE_LAYOUT_ANALYSIS.md)** - Detailed breakdown of each page section with implementation details
5. **[DEVELOPER_IMPLEMENTATION_GUIDE.md](./DEVELOPER_IMPLEMENTATION_GUIDE.md)** - Step-by-step instructions for independent implementation
6. **[ENHANCEMENT_RECOMMENDATIONS.md](./ENHANCEMENT_RECOMMENDATIONS.md)** - Modern improvements and future enhancements

## 🏗️ Architecture Overview

### Technology Stack
- **Frontend**: React 18+ with TypeScript
- **Framework**: Next.js 14+ with App Router
- **Styling**: Tailwind CSS with custom design tokens
- **Animations**: Framer Motion
- **State Management**: Zustand
- **Forms**: React Hook Form with Zod validation
- **Testing**: Jest, React Testing Library, Playwright

### Key Features
- **Responsive Design**: Mobile-first approach with all breakpoints
- **Accessibility**: WCAG 2.2 AA compliance with AAA enhancements
- **Performance**: Core Web Vitals optimization
- **SEO**: Advanced schema markup and meta optimization
- **PWA**: Progressive Web App capabilities
- **Modern UX**: Smooth animations and micro-interactions

## 🎨 Design System Highlights

### Brand Identity: Apothecary Extracts
- **Positioning**: Traditional apothecary meets modern cannabis science
- **Tone**: Scientific yet accessible, healing-focused, professional with warmth
- **Visual Style**: Botanical motifs, natural textures, vintage apothecary elements

### Color Palette
- **Primary Sage**: #87A96B (Sage green - main brand color)
- **Secondary Amber**: #D4A574 (Warm amber for premium feel)
- **Neutral Cream**: #F5F5DC (Warm cream backgrounds)
- **Status Colors**: Natural color coding for success, warning, error states

### Typography
- **Primary Font**: Inter (Clean, modern sans-serif)
- **Secondary Font**: Playfair Display (Elegant serif for headings)
- **Scale**: 12px to 60px with consistent line heights

### Component System
- **50+ Components**: From basic UI to specialized cannabis industry components
- **TypeScript Interfaces**: Complete type safety
- **Cannabis Compliance**: Age verification, legal disclaimers, purchase limits
- **Accessibility First**: Built-in ARIA support and keyboard navigation

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn
- Basic React/TypeScript knowledge

### Implementation Steps
1. Navigate to the `apothecary-extracts/` directory
2. Install dependencies with `npm install`
3. Set up environment variables (copy `.env.example` to `.env.local`)
4. Run development server with `npm run dev`
5. Follow the documentation for customization

### Key Commands
```bash
# Navigate to project directory
cd apothecary-extracts

# Install dependencies
npm install

# Development
npm run dev

# Testing
npm run test
npm run test:e2e

# Build
npm run build

# Start production server
npm start
```

## 📊 Website Analysis Summary

### Original Website Structure
- **Homepage**: Hero, location selector, product grid, testimonials, FAQ
- **About Page**: Mission statement, detailed FAQ, location information
- **Shop Page**: Product categories with location-based inventory
- **Navigation**: Multi-level menu with mobile hamburger
- **Special Features**: Age verification, location switching, pre-order status

### Content Strategy
- **Tone**: Professional yet approachable, educational, community-focused
- **SEO**: Location-based keywords, educational content for authority
- **Compliance**: Age verification, legal disclaimers, state regulations
- **Social Proof**: Customer testimonials, Google reviews integration

## 🎯 Enhancement Opportunities

### Performance Improvements
- **Core Web Vitals**: Target LCP <2.5s, FID <100ms, CLS <0.1
- **Image Optimization**: WebP/AVIF formats with progressive loading
- **Code Splitting**: Route and component-based splitting
- **Caching Strategy**: Edge caching and service worker implementation

### Accessibility Enhancements
- **WCAG 2.2 AAA**: Beyond basic compliance
- **Screen Reader**: Enhanced ARIA support and landmarks
- **Keyboard Navigation**: Complete keyboard accessibility
- **Cognitive Accessibility**: Reading level indicators and content summaries

### Modern Features
- **PWA Capabilities**: Offline functionality and push notifications
- **AI Integration**: Chatbot support and smart recommendations
- **Advanced Analytics**: Privacy-first tracking and user insights
- **Personalization**: User preferences and recommendation engine

## 📱 Responsive Design

### Breakpoints
- **Mobile**: 320px - 640px
- **Tablet**: 640px - 1024px
- **Desktop**: 1024px - 1280px
- **Large Desktop**: 1280px+

### Mobile Optimizations
- Touch-friendly interactions (44px minimum)
- Simplified navigation patterns
- Optimized image sizes
- Gesture support for carousels

## 🔒 Security & Compliance

### Cannabis Industry Compliance
- Age verification system
- Location-based content restrictions
- Legal disclaimer management
- State regulation adherence

### Modern Security
- Content Security Policy (CSP)
- HTTPS enforcement
- Privacy-first analytics
- GDPR/CCPA compliance features

## 📈 SEO Strategy

### Technical SEO
- Schema.org markup for local business
- XML sitemaps with priorities
- Canonical URL management
- Open Graph and Twitter Cards

### Content SEO
- Location-based landing pages
- Educational content clusters
- FAQ schema for featured snippets
- Local business optimization

## 🧪 Testing Strategy

### Automated Testing
- **Unit Tests**: Component and utility testing
- **Integration Tests**: User flow testing
- **E2E Tests**: Critical path validation
- **Accessibility Tests**: Automated a11y checking
- **Performance Tests**: Core Web Vitals monitoring

### Quality Assurance
- Cross-browser compatibility
- Device testing (mobile, tablet, desktop)
- Screen reader testing
- Performance auditing

## 📚 Documentation Standards

### Code Documentation
- TypeScript interfaces for all components
- Storybook stories with examples
- Inline code comments for complex logic
- README files for each major component

### Design Documentation
- Component specifications with variants
- Usage guidelines and best practices
- Accessibility requirements
- Performance considerations

## 🚀 Deployment & Monitoring

### Recommended Platforms
- **Vercel**: Optimal for Next.js applications
- **Netlify**: Alternative with good performance
- **AWS**: Enterprise-grade deployment
- **Docker**: Containerized deployment option

### Monitoring & Analytics
- Real User Monitoring (RUM)
- Core Web Vitals tracking
- Error monitoring with Sentry
- Privacy-compliant analytics

## 🤝 Contributing

This project serves as a comprehensive template and guide. When implementing:

1. Follow the component architecture guidelines
2. Maintain TypeScript type safety
3. Ensure accessibility standards
4. Test thoroughly before deployment
5. Document any deviations or improvements

## 📄 License

This project is provided for educational and development purposes. All content and branding is for Apothecary Extracts.

## 🔗 Resources


- [React Documentation](https://react.dev/)
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com/)
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG22/quickref/)

---

**Apothecary Extracts - Where Traditional Apothecary Meets Modern Cannabis Science** 🌿
