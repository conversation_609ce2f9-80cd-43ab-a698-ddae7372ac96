import { Metadata } from 'next';
import { <PERSON> } from '@/components/content/Hero';
import { Section } from '@/components/layout/Section';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';

export const metadata: Metadata = {
  title: 'Dispensary Locations | Apothecary Farms',
  description: 'Find Apothecary Farms dispensary locations across Colorado and Oklahoma. Award-winning concentrates, premium cannabis products, store hours, and directions.',
  keywords: 'cannabis dispensary locations, Colorado dispensary, Oklahoma dispensary, concentrates, Apothecary Extracts, marijuana store',
};

const locations = [
  {
    id: 'colorado-springs',
    name: 'Colorado Springs (Flagship)',
    address: {
      street: '3049 Delta Dr',
      city: 'Colorado Springs',
      state: 'CO',
      zipCode: '80910',
    },
    phone: '(*************',
    email: '<EMAIL>',
    hours: {
      weekdays: '8:00 AM - 8:50 PM',
      saturday: '8:00 AM - 8:50 PM',
      sunday: '8:00 AM - 8:50 PM',
    },
    services: ['Medical', 'Recreational (Starting April 2025)', 'Curbside', 'Consultation'],
    amenities: ['Parking Available', 'Wheelchair Accessible', 'Concentrate Lab', 'Educational Resources'],
    description: 'Our flagship location featuring state-of-the-art concentrate production facilities and our full selection of award-winning Apothecary Extracts products.',
    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
  },
  {
    id: 'denver',
    name: 'Denver',
    address: {
      street: '2251 S Broadway',
      city: 'Denver',
      state: 'CO',
      zipCode: '80210',
    },
    phone: '(*************',
    email: '<EMAIL>',
    hours: {
      weekdays: '8:00 AM - 9:50 PM',
      saturday: '8:00 AM - 9:50 PM',
      sunday: '8:00 AM - 9:50 PM',
    },
    services: ['Recreational', 'Medical', 'Delivery', 'Curbside'],
    amenities: ['Street Parking', 'Wheelchair Accessible', 'Express Pickup', 'Online Ordering'],
    description: 'Located on South Broadway, our Denver location offers both recreational and medical cannabis with our signature concentrate selection.',
    image: 'https://images.unsplash.com/photo-**********-195a672e8a03?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
  },
  {
    id: 'pueblo',
    name: 'Pueblo',
    address: {
      street: '1917 Santa Fe Dr',
      city: 'Pueblo',
      state: 'CO',
      zipCode: '81006',
    },
    phone: '(*************',
    email: '<EMAIL>',
    hours: {
      weekdays: '9:00 AM - 9:00 PM',
      saturday: '9:00 AM - 9:00 PM',
      sunday: '10:00 AM - 8:00 PM',
    },
    services: ['Recreational', 'Medical', 'Curbside'],
    amenities: ['Parking Available', 'Wheelchair Accessible', 'Local Discounts', 'Product Education'],
    description: 'Serving the Pueblo community with premium cannabis products and our award-winning concentrate selection in a welcoming environment.',
    image: 'https://images.unsplash.com/photo-1582719471384-894fbb16e074?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
  },
  {
    id: 'oklahoma-city',
    name: 'Oklahoma City',
    address: {
      street: '7941 N May Ave',
      city: 'Oklahoma City',
      state: 'OK',
      zipCode: '73120',
    },
    phone: '(*************',
    email: '<EMAIL>',
    hours: {
      weekdays: '9:00 AM - 10:00 PM',
      saturday: '9:00 AM - 10:00 PM',
      sunday: '11:00 AM - 10:00 PM',
    },
    services: ['Medical', 'Curbside', 'Consultation'],
    amenities: ['Parking Available', 'Wheelchair Accessible', 'Patient Education', 'Loyalty Program'],
    description: 'Our Oklahoma City location serves medical cannabis patients with our full range of products and expert guidance from our knowledgeable staff.',
    image: 'https://images.unsplash.com/photo-**********-c3190ca9959b?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
  },
  {
    id: 'tulsa',
    name: 'Tulsa',
    address: {
      street: '4942 S Union Ave',
      city: 'Tulsa',
      state: 'OK',
      zipCode: '74107',
    },
    phone: '(*************',
    email: '<EMAIL>',
    hours: {
      weekdays: '9:00 AM - 10:00 PM',
      saturday: '9:00 AM - 10:00 PM',
      sunday: '10:00 AM - 9:00 PM',
    },
    services: ['Medical', 'Curbside', 'Consultation'],
    amenities: ['Parking Available', 'Wheelchair Accessible', 'Patient Consultations', 'Educational Resources'],
    description: 'Located in South Tulsa, we provide medical cannabis patients with premium products and personalized service in a modern, welcoming environment.',
    image: 'https://images.unsplash.com/photo-1574263867128-a3d5c1b1deaa?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
  },
  {
    id: 'moore',
    name: 'Moore',
    address: {
      street: '2100 Riverwalk Dr Suite A',
      city: 'Moore',
      state: 'OK',
      zipCode: '73160',
    },
    phone: '(*************',
    email: '<EMAIL>',
    hours: {
      weekdays: '9:00 AM - 10:00 PM',
      saturday: '9:00 AM - 10:00 PM',
      sunday: '10:00 AM - 9:00 PM',
    },
    services: ['Medical', 'Curbside', 'Consultation'],
    amenities: ['Parking Available', 'Wheelchair Accessible', 'Drive-Through', 'Express Service'],
    description: 'Conveniently located in Moore, our newest Oklahoma location features drive-through service and our complete selection of medical cannabis products.',
    image: 'https://images.unsplash.com/photo-**********-7cc5ac882d5f?w=800&h=600&fit=crop&crop=center&auto=format&q=80',
  },
];

export default function LocationsPage() {
  return (
    <>
      {/* Hero Section */}
      <Hero
        subtitle="Our Locations"
        title="Visit Apothecary Farms"
        description="Find a dispensary near you across Colorado and Oklahoma. Each location features our award-winning Apothecary Extracts concentrates and premium cannabis products with expert guidance."
        backgroundImage="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1920&h=1080&fit=crop&crop=center&auto=format&q=80"
        primaryCTA={{
          text: "Shop Online",
          href: "/shop",
          icon: "cart"
        }}
        secondaryCTA={{
          text: "Contact Us",
          href: "/contact",
          icon: "phone"
        }}
      />

      {/* Locations Grid */}
      <Section padding="xl" background="white">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-charcoal mb-4 font-accent">
              Our Dispensary Locations
            </h2>
            <p className="text-lg text-neutral-charcoal/70 max-w-2xl mx-auto">
              Six convenient locations across Colorado and Oklahoma, each featuring our award-winning Apothecary Extracts concentrates and premium cannabis products.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-8">
            {locations.map((location) => (
              <Card key={location.id} className="overflow-hidden">
                {/* Location Image */}
                <div className="aspect-video bg-gradient-to-br from-primary-sage to-primary-forest flex items-center justify-center">
                  <div className="text-center text-neutral-white">
                    <Icon name="map-pin" size="2xl" className="mx-auto mb-2" />
                    <p className="text-lg font-medium">{location.name} Location</p>
                  </div>
                </div>

                <CardContent className="p-6">
                  <CardHeader className="p-0 mb-4">
                    <CardTitle className="text-2xl font-accent">
                      Apothecary Farms - {location.name}
                    </CardTitle>
                  </CardHeader>

                  <p className="text-neutral-charcoal/70 mb-6">
                    {location.description}
                  </p>

                  {/* Contact Info */}
                  <div className="space-y-3 mb-6">
                    <div className="flex items-center space-x-3">
                      <Icon name="map-pin" size="sm" className="text-primary-sage" />
                      <span className="text-neutral-charcoal">
                        {location.address.street}, {location.address.city}, {location.address.state} {location.address.zipCode}
                      </span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Icon name="phone" size="sm" className="text-primary-sage" />
                      <span className="text-neutral-charcoal">{location.phone}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Icon name="mail" size="sm" className="text-primary-sage" />
                      <span className="text-neutral-charcoal">{location.email}</span>
                    </div>
                  </div>

                  {/* Hours */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-neutral-charcoal mb-2">Store Hours</h4>
                    <div className="text-sm text-neutral-charcoal/70 space-y-1">
                      <div className="flex justify-between">
                        <span>Monday - Friday:</span>
                        <span>{location.hours.weekdays}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Saturday:</span>
                        <span>{location.hours.saturday}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Sunday:</span>
                        <span>{location.hours.sunday}</span>
                      </div>
                    </div>
                  </div>

                  {/* Services */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-neutral-charcoal mb-2">Services</h4>
                    <div className="flex flex-wrap gap-2">
                      {location.services.map((service) => (
                        <span
                          key={service}
                          className="inline-flex items-center px-2 py-1 text-xs font-medium bg-primary-sage/10 text-primary-sage rounded-full"
                        >
                          {service}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* Amenities */}
                  <div className="mb-6">
                    <h4 className="font-semibold text-neutral-charcoal mb-2">Amenities</h4>
                    <div className="grid grid-cols-2 gap-2 text-sm text-neutral-charcoal/70">
                      {location.amenities.map((amenity) => (
                        <div key={amenity} className="flex items-center space-x-2">
                          <Icon name="check" size="sm" className="text-primary-sage" />
                          <span>{amenity}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button variant="primary" fullWidth>
                      Get Directions
                    </Button>
                    <Button variant="secondary" fullWidth>
                      Call Store
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </Section>

      {/* General Information */}
      <Section padding="xl" background="cream">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-charcoal mb-4 font-accent">
              What to Expect
            </h2>
            <p className="text-lg text-neutral-charcoal/70">
              Your first visit to Apothecary Farms
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            <Card className="p-6">
              <CardContent className="p-0">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Icon name="user" size="lg" className="text-primary-sage" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-neutral-charcoal mb-2">What to Bring</h3>
                    <ul className="text-neutral-charcoal/70 space-y-1 text-sm">
                      <li>• Valid government-issued photo ID</li>
                      <li>• Medical marijuana card (if applicable)</li>
                      <li>• Cash or debit card for payment</li>
                      <li>• Questions about products or dosing</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="p-6">
              <CardContent className="p-0">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 bg-primary-sage/10 rounded-lg flex items-center justify-center flex-shrink-0">
                    <Icon name="heart" size="lg" className="text-primary-sage" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-neutral-charcoal mb-2">Our Service</h3>
                    <ul className="text-neutral-charcoal/70 space-y-1 text-sm">
                      <li>• Personalized product consultations</li>
                      <li>• Educational resources and guidance</li>
                      <li>• Professional, welcoming environment</li>
                      <li>• Respect for your privacy and needs</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </Section>

      {/* CTA Section */}
      <Section padding="xl" background="sage" className="text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold text-neutral-white mb-6 font-accent">
            Ready to Visit?
          </h2>
          <p className="text-xl text-neutral-white/90 mb-8">
            Stop by one of our locations or place an order online for pickup. 
            Our knowledgeable staff is here to help you find the right products for your needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/shop"
              className="inline-flex items-center px-8 py-3 text-lg font-medium text-primary-sage bg-neutral-white rounded-md hover:bg-neutral-cream transition-colors duration-200"
            >
              Shop Online
            </a>
            <a
              href="/contact"
              className="inline-flex items-center px-8 py-3 text-lg font-medium text-neutral-white border border-neutral-white rounded-md hover:bg-neutral-white hover:text-primary-sage transition-colors duration-200"
            >
              Contact Us
            </a>
          </div>
        </div>
      </Section>
    </>
  );
}
