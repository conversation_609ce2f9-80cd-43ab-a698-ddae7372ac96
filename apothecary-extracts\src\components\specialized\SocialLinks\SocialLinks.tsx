import React from 'react';
import { cn } from '@/lib/utils';
import { Icon, type IconName } from '@/components/ui/Icon';
import { SOCIAL_LINKS } from '@/lib/constants';

export interface SocialLink {
  name: string;
  url: string;
  icon: IconName;
  label: string;
}

export interface SocialLinksProps {
  links?: SocialLink[];
  variant?: 'horizontal' | 'vertical';
  size?: 'sm' | 'md' | 'lg';
  showLabels?: boolean;
  className?: string;
}

const defaultSocialLinks: SocialLink[] = [
  {
    name: 'facebook',
    url: SOCIAL_LINKS.facebook,
    icon: 'facebook',
    label: 'Follow us on Facebook',
  },
  {
    name: 'instagram',
    url: SOCIAL_LINKS.instagram,
    icon: 'instagram',
    label: 'Follow us on Instagram',
  },
  {
    name: 'twitter',
    url: SOCIAL_LINKS.twitter,
    icon: 'twitter',
    label: 'Follow us on Twitter',
  },
  {
    name: 'youtube',
    url: SOCIAL_LINKS.youtube,
    icon: 'youtube',
    label: 'Subscribe to our YouTube channel',
  },
  {
    name: 'linkedin',
    url: SOCIAL_LINKS.linkedin,
    icon: 'linkedin',
    label: 'Connect with us on LinkedIn',
  },
];

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-10 h-10',
  lg: 'w-12 h-12',
} as const;

const iconSizes = {
  sm: 'sm' as const,
  md: 'md' as const,
  lg: 'lg' as const,
};

export const SocialLinks: React.FC<SocialLinksProps> = ({
  links = defaultSocialLinks,
  variant = 'horizontal',
  size = 'md',
  showLabels = false,
  className,
}) => {
  return (
    <div
      className={cn(
        'flex gap-3',
        variant === 'vertical' ? 'flex-col' : 'flex-row items-center',
        className
      )}
    >
      {links.map((link) => (
        <a
          key={link.name}
          href={link.url}
          target="_blank"
          rel="noopener noreferrer"
          className={cn(
            'inline-flex items-center justify-center rounded-full transition-all duration-200',
            'text-neutral-charcoal/60 hover:text-primary-sage',
            'hover:bg-primary-sage/10 hover:scale-110',
            'focus:outline-none focus:ring-2 focus:ring-primary-sage focus:ring-offset-2',
            sizeClasses[size],
            showLabels && variant === 'horizontal' && 'gap-2 px-4 py-2 w-auto h-auto rounded-lg'
          )}
          aria-label={link.label}
        >
          <Icon 
            name={link.icon} 
            size={iconSizes[size]} 
            className="flex-shrink-0"
          />
          {showLabels && (
            <span className="text-sm font-medium capitalize">
              {link.name}
            </span>
          )}
        </a>
      ))}
    </div>
  );
};

// Preset configurations for common use cases
export const SocialLinksFooter: React.FC<Omit<SocialLinksProps, 'variant' | 'size'>> = (props) => (
  <SocialLinks {...props} variant="horizontal" size="md" />
);

export const SocialLinksHeader: React.FC<Omit<SocialLinksProps, 'variant' | 'size'>> = (props) => (
  <SocialLinks {...props} variant="horizontal" size="sm" />
);

export const SocialLinksSidebar: React.FC<Omit<SocialLinksProps, 'variant' | 'size'>> = (props) => (
  <SocialLinks {...props} variant="vertical" size="lg" />
);

export default SocialLinks;
