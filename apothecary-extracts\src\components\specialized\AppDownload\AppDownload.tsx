import React from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/Card';
import { Icon } from '@/components/ui/Icon';
import { APP_STORE_LINKS } from '@/lib/constants';

export interface AppDownloadProps {
  title?: string;
  description?: string;
  appStoreUrl?: string;
  playStoreUrl?: string;
  showQR?: boolean;
  variant?: 'card' | 'inline' | 'banner';
  className?: string;
}

export const AppDownload: React.FC<AppDownloadProps> = ({
  title = "Download Our App",
  description = "Get the Apothecary Extracts mobile app for easy ordering, exclusive deals, and product information on the go.",
  appStoreUrl = APP_STORE_LINKS.ios,
  playStoreUrl = APP_STORE_LINKS.android,
  showQR = false,
  variant = 'card',
  className,
}) => {
  const content = (
    <div className={cn(
      'flex flex-col items-center text-center space-y-6',
      variant === 'inline' && 'md:flex-row md:text-left md:space-y-0 md:space-x-8'
    )}>
      {/* App Icon/Mockup */}
      <div className="flex-shrink-0">
        <div className="w-24 h-24 bg-gradient-to-br from-primary-sage to-primary-forest rounded-2xl flex items-center justify-center shadow-lg">
          <Icon name="leaf" size="xl" className="text-neutral-white" />
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 space-y-4">
        <div>
          <h3 className="text-xl md:text-2xl font-bold text-neutral-charcoal mb-2 font-accent">
            {title}
          </h3>
          <p className="text-neutral-charcoal/70 max-w-md mx-auto md:mx-0">
            {description}
          </p>
        </div>

        {/* Features */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm text-neutral-charcoal/70">
          <div className="flex items-center space-x-2">
            <Icon name="check" size="sm" className="text-primary-sage flex-shrink-0" />
            <span>Easy online ordering</span>
          </div>
          <div className="flex items-center space-x-2">
            <Icon name="check" size="sm" className="text-primary-sage flex-shrink-0" />
            <span>Exclusive app deals</span>
          </div>
          <div className="flex items-center space-x-2">
            <Icon name="check" size="sm" className="text-primary-sage flex-shrink-0" />
            <span>Product information</span>
          </div>
          <div className="flex items-center space-x-2">
            <Icon name="check" size="sm" className="text-primary-sage flex-shrink-0" />
            <span>Order tracking</span>
          </div>
        </div>

        {/* Download Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center md:justify-start">
          <a
            href={appStoreUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center justify-center px-4 py-2 bg-neutral-black text-neutral-white rounded-lg hover:bg-neutral-charcoal transition-colors duration-200"
            aria-label="Download on the App Store"
          >
            <div className="flex items-center space-x-2">
              <Icon name="download" size="sm" />
              <div className="text-left">
                <div className="text-xs">Download on the</div>
                <div className="text-sm font-semibold">App Store</div>
              </div>
            </div>
          </a>

          <a
            href={playStoreUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center justify-center px-4 py-2 bg-neutral-black text-neutral-white rounded-lg hover:bg-neutral-charcoal transition-colors duration-200"
            aria-label="Get it on Google Play"
          >
            <div className="flex items-center space-x-2">
              <Icon name="download" size="sm" />
              <div className="text-left">
                <div className="text-xs">Get it on</div>
                <div className="text-sm font-semibold">Google Play</div>
              </div>
            </div>
          </a>
        </div>

        {/* QR Code (if enabled) */}
        {showQR && (
          <div className="pt-4">
            <p className="text-sm text-neutral-charcoal/60 mb-3">
              Scan to download:
            </p>
            <div className="w-24 h-24 bg-neutral-stone rounded-lg flex items-center justify-center mx-auto md:mx-0">
              <Icon name="external-link" size="lg" className="text-neutral-charcoal/40" />
            </div>
          </div>
        )}
      </div>
    </div>
  );

  if (variant === 'card') {
    return (
      <Card className={cn('p-6', className)}>
        <CardContent className="p-0">
          {content}
        </CardContent>
      </Card>
    );
  }

  if (variant === 'banner') {
    return (
      <div className={cn(
        'bg-gradient-to-r from-primary-sage to-primary-forest text-neutral-white p-6 rounded-lg',
        className
      )}>
        {content}
      </div>
    );
  }

  return (
    <div className={className}>
      {content}
    </div>
  );
};

export default AppDownload;
