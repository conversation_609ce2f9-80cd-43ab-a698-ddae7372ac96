globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/_not-found/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/forms/NewsletterForm/NewsletterForm.tsx":{"*":{"id":"(ssr)/./src/components/forms/NewsletterForm/NewsletterForm.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header/Header.tsx":{"*":{"id":"(ssr)/./src/components/layout/Header/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header/LocationSelector.tsx":{"*":{"id":"(ssr)/./src/components/layout/Header/LocationSelector.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header/MobileMenu.tsx":{"*":{"id":"(ssr)/./src/components/layout/Header/MobileMenu.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header/NavigationMenu.tsx":{"*":{"id":"(ssr)/./src/components/layout/Header/NavigationMenu.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/specialized/AgeVerification/AgeGate.tsx":{"*":{"id":"(ssr)/./src/components/specialized/AgeVerification/AgeGate.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/content/Hero/Hero.tsx":{"*":{"id":"(ssr)/./src/components/content/Hero/Hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\forms\\NewsletterForm\\NewsletterForm.tsx":{"id":"(app-pages-browser)/./src/components/forms/NewsletterForm/NewsletterForm.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\Header.tsx":{"id":"(app-pages-browser)/./src/components/layout/Header/Header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\LocationSelector.tsx":{"id":"(app-pages-browser)/./src/components/layout/Header/LocationSelector.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\MobileMenu.tsx":{"id":"(app-pages-browser)/./src/components/layout/Header/MobileMenu.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\layout\\Header\\NavigationMenu.tsx":{"id":"(app-pages-browser)/./src/components/layout/Header/NavigationMenu.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\specialized\\AgeVerification\\AgeGate.tsx":{"id":"(app-pages-browser)/./src/components/specialized/AgeVerification/AgeGate.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\components\\content\\Hero\\Hero.tsx":{"id":"(app-pages-browser)/./src/components/content/Hero/Hero.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\":[],"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\page":[],"O:\\VSCODE PROJECTS\\2025 NEW\\HeirloomCollective\\apothecary-extracts\\src\\app\\_not-found\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/forms/NewsletterForm/NewsletterForm.tsx":{"*":{"id":"(rsc)/./src/components/forms/NewsletterForm/NewsletterForm.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header/Header.tsx":{"*":{"id":"(rsc)/./src/components/layout/Header/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header/LocationSelector.tsx":{"*":{"id":"(rsc)/./src/components/layout/Header/LocationSelector.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header/MobileMenu.tsx":{"*":{"id":"(rsc)/./src/components/layout/Header/MobileMenu.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header/NavigationMenu.tsx":{"*":{"id":"(rsc)/./src/components/layout/Header/NavigationMenu.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/specialized/AgeVerification/AgeGate.tsx":{"*":{"id":"(rsc)/./src/components/specialized/AgeVerification/AgeGate.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/content/Hero/Hero.tsx":{"*":{"id":"(rsc)/./src/components/content/Hero/Hero.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}