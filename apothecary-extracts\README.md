# Apothecary Extracts - Cannabis Dispensary Website

A modern, responsive cannabis dispensary website built with Next.js 14+, TypeScript, and Tailwind CSS. Features age verification, location-based shopping, and comprehensive product management.

## 🌿 Features

### Cannabis Industry Compliance
- **Age Verification**: Mandatory age gate with localStorage persistence
- **Legal Disclaimers**: Comprehensive compliance messaging
- **Purchase Limits**: Recreational and medical purchase limit tracking
- **Location-Based Services**: Multi-dispensary support with location switching

### Modern Web Technologies
- **Next.js 14+**: App Router with TypeScript
- **Tailwind CSS v4**: Custom design system with medicinal color palette
- **Framer Motion**: Smooth animations and transitions
- **Zustand**: Lightweight state management
- **React Hook Form**: Form validation and handling

### User Experience
- **Responsive Design**: Mobile-first approach with touch-friendly interactions
- **Accessibility**: WCAG 2.2 AA compliance built-in
- **Performance**: Optimized images, lazy loading, and code splitting
- **SEO Optimized**: Proper metadata, Open Graph, and Twitter Cards

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

## 🎨 Design System

### Brand Identity: Apothecary Extracts
- **Positioning**: Traditional apothecary meets modern cannabis science
- **Tone**: Scientific yet accessible, healing-focused, professional with warmth

### Color Palette
```css
/* Primary Colors */
--primary-sage: #87A96B;      /* Main brand color */
--primary-forest: #4A5D23;    /* Dark accent */
--primary-mint: #A8C090;      /* Light accent */

/* Secondary Colors */
--secondary-amber: #D4A574;   /* Warm accent */
--secondary-clay: #B8956A;    /* Earthy tone */
--secondary-lavender: #9B8AA3; /* Soft accent */
```

## 📁 Project Structure

```
apothecary-extracts/
├── src/
│   ├── app/                    # Next.js App Router pages
│   ├── components/            # React components
│   │   ├── layout/           # Header, Footer, Container, Section
│   │   ├── ui/               # Button, Card, Modal, Icon
│   │   ├── content/          # Hero, ProductGrid, etc.
│   │   └── specialized/      # AgeGate, LocationSelector
│   ├── hooks/                # Custom React hooks
│   ├── lib/                  # Utilities and configuration
│   ├── store/                # Zustand state management
│   └── data/                 # Static data
├── public/                   # Static assets
└── docs/                    # Documentation
```

## 🧩 Key Components

### Age Verification (`AgeGate`)
- Mandatory age verification for cannabis compliance
- Date of birth validation (21+ recreational, 18+ medical)
- localStorage persistence

### Location Management (`LocationSelector`)
- Multi-location dispensary support
- Cart warning when switching locations
- Store hours and service information

### Product Grid (`ProductGrid`)
- Responsive product category display
- Hover effects and animations
- Mobile-optimized layout

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage
```

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

## ♿ Accessibility

### WCAG 2.2 AA Compliance
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and roles
- **Color Contrast**: 4.5:1 minimum contrast ratio
- **Semantic HTML**: Proper heading hierarchy

## 📄 License

This project is licensed under the MIT License.

---

**Built with ❤️ for the cannabis community**
