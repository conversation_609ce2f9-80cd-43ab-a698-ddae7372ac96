import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Card, CardContent } from '@/components/ui/Card';
import { Icon } from '@/components/ui/Icon';
import type { ProductCategoryData } from '@/data/products';

export interface ProductCardProps {
  product: ProductCategoryData;
  className?: string;
}

export const ProductCard: React.FC<ProductCardProps> = ({
  product,
  className,
}) => {
  return (
    <Link href={product.href} className={className}>
      <Card 
        variant="product" 
        padding="none" 
        hover
        className="group overflow-hidden h-full"
      >
        {/* Image */}
        <div className="relative aspect-square overflow-hidden">
          <Image
            src={product.image}
            alt={product.name}
            fill
            className="object-cover transition-transform duration-300 group-hover:scale-105"
            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
          />
          
          {/* Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          
          {/* Featured badge */}
          {product.featured && (
            <div className="absolute top-3 left-3">
              <span className="inline-flex items-center px-2 py-1 text-xs font-medium text-neutral-white bg-secondary-amber rounded-full">
                <Icon name="star" size="xs" className="mr-1" />
                Featured
              </span>
            </div>
          )}
          
          {/* Hover icon */}
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className="bg-neutral-white/90 rounded-full p-3">
              <Icon name="arrow-right" size="lg" className="text-primary-sage" />
            </div>
          </div>
        </div>

        {/* Content */}
        <CardContent className="p-4">
          <h3 className="text-lg font-semibold text-neutral-charcoal mb-2 group-hover:text-primary-sage transition-colors duration-200">
            {product.name}
          </h3>
          
          <p className="text-sm text-neutral-charcoal/70 line-clamp-2">
            {product.description}
          </p>
          
          {/* Shop now link */}
          <div className="mt-3 flex items-center text-primary-sage text-sm font-medium">
            <span>Shop Now</span>
            <Icon name="arrow-right" size="sm" className="ml-1 transition-transform duration-200 group-hover:translate-x-1" />
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};

export default ProductCard;
