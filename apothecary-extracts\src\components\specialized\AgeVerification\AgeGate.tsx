'use client';

import React, { useState } from 'react';
import { Modal, ModalBody } from '@/components/ui/Modal';
import { Button } from '@/components/ui/Button';
import { Icon } from '@/components/ui/Icon';
import { useAgeVerification } from '@/hooks/useAgeVerification';
import { useUIStore } from '@/store/uiStore';
import { SOCIAL_LINKS } from '@/lib/constants';

export const AgeGate: React.FC = () => {
  const [birthDate, setBirthDate] = useState('');
  const [error, setError] = useState('');
  
  const { handleVerifyAge, handleDenyAge } = useAgeVerification();
  const { isAgeGateOpen } = useUIStore();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!birthDate) {
      setError('Please enter your date of birth');
      return;
    }

    const isValid = handleVerifyAge(birthDate);
    
    if (!isValid) {
      setError('You must be 21 or older to access this site');
      setTimeout(() => {
        handleDenyAge();
      }, 2000);
    }
  };

  const handleDeny = () => {
    handleDenyAge();
  };

  return (
    <Modal
      isOpen={isAgeGateOpen}
      onClose={() => {}} // Prevent closing
      showCloseButton={false}
      closeOnOverlayClick={false}
      closeOnEscape={false}
      size="md"
      className="bg-gradient-to-br from-primary-sage to-primary-forest text-neutral-white"
    >
      <ModalBody>
        <div className="text-center">
          {/* Logo */}
          <div className="mb-8">
            <div className="w-16 h-16 bg-neutral-white rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="leaf" size="xl" className="text-primary-sage" />
            </div>
            <h1 className="text-2xl font-bold font-accent">Apothecary Extracts</h1>
            <p className="text-neutral-white/80 mt-2">Premium Medicinal Cannabis</p>
          </div>

          {/* Age Verification */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Age Verification Required</h2>
            <p className="text-neutral-white/90 mb-6">
              You must be 21 years or older to access this website. Please verify your age to continue.
            </p>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="birthDate" className="block text-sm font-medium mb-2">
                  Date of Birth
                </label>
                <input
                  type="date"
                  id="birthDate"
                  value={birthDate}
                  onChange={(e) => setBirthDate(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg bg-neutral-white text-neutral-charcoal border border-neutral-stone focus:ring-2 focus:ring-secondary-amber focus:border-transparent"
                  max={new Date().toISOString().split('T')[0]}
                />
              </div>

              {error && (
                <div className="text-secondary-amber text-sm font-medium">
                  {error}
                </div>
              )}

              <div className="flex space-x-4">
                <Button
                  type="submit"
                  variant="accent"
                  size="lg"
                  fullWidth
                  className="bg-secondary-amber hover:bg-secondary-clay text-neutral-charcoal"
                >
                  I am 21 or older
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="lg"
                  fullWidth
                  onClick={handleDeny}
                  className="border border-neutral-white/30 text-neutral-white hover:bg-neutral-white/10"
                >
                  I am under 21
                </Button>
              </div>
            </form>
          </div>

          {/* Legal Notice */}
          <div className="text-xs text-neutral-white/70 mb-6">
            <p>
              By entering this site, you acknowledge that you are of legal age and that you agree to our{' '}
              <a href="/terms" className="underline hover:text-neutral-white">Terms of Service</a> and{' '}
              <a href="/privacy" className="underline hover:text-neutral-white">Privacy Policy</a>.
            </p>
          </div>

          {/* Social Links */}
          <div className="flex justify-center space-x-4">
            <a
              href={SOCIAL_LINKS.facebook}
              target="_blank"
              rel="noopener noreferrer"
              className="text-neutral-white/60 hover:text-neutral-white transition-colors duration-200"
              aria-label="Follow us on Facebook"
            >
              <Icon name="facebook" size="md" />
            </a>
            <a
              href={SOCIAL_LINKS.instagram}
              target="_blank"
              rel="noopener noreferrer"
              className="text-neutral-white/60 hover:text-neutral-white transition-colors duration-200"
              aria-label="Follow us on Instagram"
            >
              <Icon name="instagram" size="md" />
            </a>
            <a
              href={SOCIAL_LINKS.twitter}
              target="_blank"
              rel="noopener noreferrer"
              className="text-neutral-white/60 hover:text-neutral-white transition-colors duration-200"
              aria-label="Follow us on Twitter"
            >
              <Icon name="twitter" size="md" />
            </a>
          </div>
        </div>
      </ModalBody>
    </Modal>
  );
};

export default AgeGate;
