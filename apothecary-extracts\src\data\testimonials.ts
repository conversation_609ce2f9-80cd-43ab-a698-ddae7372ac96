export interface TestimonialData {
  id: string;
  content: string;
  author: string;
  location: string;
  rating: number;
  source: 'google' | 'leafly' | 'weedmaps' | 'internal';
  date: string;
  verified: boolean;
}

export const testimonials: TestimonialData[] = [
  {
    id: 'test_1',
    content: "Apothecary Extracts has completely changed my perspective on cannabis. Their knowledgeable staff helped me find the perfect tincture for my chronic pain, and the quality is unmatched. The downtown location feels more like a high-end pharmacy than a dispensary.",
    author: "<PERSON> M<PERSON>",
    location: "Downtown Boston",
    rating: 5,
    source: 'google',
    date: '2024-01-15',
    verified: true,
  },
  {
    id: 'test_2',
    content: "As someone new to cannabis, I was nervous about my first visit. The team at Apothecary Extracts made me feel completely comfortable and educated me about different options. Their CBD products have been life-changing for my anxiety.",
    author: "<PERSON>",
    location: "Cambridge",
    rating: 5,
    source: 'leafly',
    date: '2024-01-10',
    verified: true,
  },
  {
    id: 'test_3',
    content: "The quality of their concentrates is exceptional. You can tell they really care about the extraction process and testing. Their live rosin is some of the best I've had in Massachusetts. Plus, the staff actually knows what they're talking about.",
    author: "<PERSON>",
    location: "Downtown Boston",
    rating: 5,
    source: 'weedmaps',
    date: '2024-01-08',
    verified: true,
  },
  {
    id: 'test_4',
    content: "I've been to many dispensaries, but Apothecary Extracts stands out for their medicinal approach. They don't just sell products; they provide genuine healthcare guidance. Their tinctures have helped me reduce my prescription medications significantly.",
    author: "David K.",
    location: "Cambridge",
    rating: 5,
    source: 'google',
    date: '2024-01-05',
    verified: true,
  },
  {
    id: 'test_5',
    content: "The atmosphere is so professional and welcoming. I brought my elderly mother here for her first cannabis consultation, and they treated her with such respect and patience. The topicals they recommended have helped her arthritis tremendously.",
    author: "Lisa T.",
    location: "Downtown Boston",
    rating: 5,
    source: 'internal',
    date: '2024-01-03',
    verified: true,
  },
  {
    id: 'test_6',
    content: "Their flower selection is incredible, and everything is properly cured and stored. The budtenders are like cannabis sommeliers - they can recommend the perfect strain based on your needs and preferences. The Cambridge location is my go-to spot.",
    author: "Alex P.",
    location: "Cambridge",
    rating: 5,
    source: 'leafly',
    date: '2023-12-28',
    verified: true,
  },
  {
    id: 'test_7',
    content: "I appreciate their focus on education and harm reduction. They offer workshops and consultations that have helped me understand cannabis as medicine rather than just recreation. Their products are consistently high quality.",
    author: "Maria G.",
    location: "Downtown Boston",
    rating: 5,
    source: 'google',
    date: '2023-12-25',
    verified: true,
  },
  {
    id: 'test_8',
    content: "The pre-rolls are perfectly rolled and burn evenly every time. You can taste the quality of the flower they use. Their packaging is also top-notch - everything stays fresh and potent. Highly recommend their hybrid selections.",
    author: "James W.",
    location: "Cambridge",
    rating: 5,
    source: 'weedmaps',
    date: '2023-12-20',
    verified: true,
  },
];

export const getTestimonialsByLocation = (location: string): TestimonialData[] => {
  return testimonials.filter(testimonial => 
    testimonial.location.toLowerCase().includes(location.toLowerCase())
  );
};

export const getTestimonialsByRating = (minRating: number): TestimonialData[] => {
  return testimonials.filter(testimonial => testimonial.rating >= minRating);
};

export const getRandomTestimonials = (count: number): TestimonialData[] => {
  const shuffled = [...testimonials].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export const getAverageRating = (): number => {
  const total = testimonials.reduce((sum, testimonial) => sum + testimonial.rating, 0);
  return Math.round((total / testimonials.length) * 10) / 10;
};

export const getTotalReviews = (): number => {
  return testimonials.length;
};
