import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { STORAGE_KEYS, TAX_RATES } from '@/lib/constants';
import { calculateTax, calculateTotal } from '@/lib/utils';
import type { Cart, CartItem, Location } from '@/lib/types';

interface CartState {
  cart: Cart | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  initializeCart: (locationId: string) => void;
  addItem: (item: Omit<CartItem, 'id' | 'addedAt'>) => void;
  removeItem: (itemId: string) => void;
  updateItemQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  switchLocation: (newLocationId: string) => void;
  calculateTotals: () => void;
  getItemCount: () => number;
  getItemById: (itemId: string) => CartItem | undefined;
  hasItem: (productId: string, variantId: string) => boolean;
}

export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      cart: null,
      isLoading: false,
      error: null,

      initializeCart: (locationId: string) => {
        const newCart: Cart = {
          id: `cart_${Date.now()}`,
          locationId,
          items: [],
          subtotal: 0,
          tax: 0,
          total: 0,
          discounts: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        
        set({ cart: newCart });
      },

      addItem: (newItem: Omit<CartItem, 'id' | 'addedAt'>) => {
        const { cart } = get();
        if (!cart) return;

        const existingItemIndex = cart.items.findIndex(
          item => item.productId === newItem.productId && item.variantId === newItem.variantId
        );

        let updatedItems: CartItem[];

        if (existingItemIndex >= 0) {
          // Update existing item quantity
          updatedItems = cart.items.map((item, index) =>
            index === existingItemIndex
              ? { ...item, quantity: item.quantity + newItem.quantity }
              : item
          );
        } else {
          // Add new item
          const cartItem: CartItem = {
            ...newItem,
            id: `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            addedAt: new Date().toISOString(),
          };
          updatedItems = [...cart.items, cartItem];
        }

        const updatedCart = {
          ...cart,
          items: updatedItems,
          updatedAt: new Date().toISOString(),
        };

        set({ cart: updatedCart });
        get().calculateTotals();
      },

      removeItem: (itemId: string) => {
        const { cart } = get();
        if (!cart) return;

        const updatedItems = cart.items.filter(item => item.id !== itemId);
        const updatedCart = {
          ...cart,
          items: updatedItems,
          updatedAt: new Date().toISOString(),
        };

        set({ cart: updatedCart });
        get().calculateTotals();
      },

      updateItemQuantity: (itemId: string, quantity: number) => {
        const { cart } = get();
        if (!cart) return;

        if (quantity <= 0) {
          get().removeItem(itemId);
          return;
        }

        const updatedItems = cart.items.map(item =>
          item.id === itemId ? { ...item, quantity } : item
        );

        const updatedCart = {
          ...cart,
          items: updatedItems,
          updatedAt: new Date().toISOString(),
        };

        set({ cart: updatedCart });
        get().calculateTotals();
      },

      clearCart: () => {
        const { cart } = get();
        if (!cart) return;

        const clearedCart = {
          ...cart,
          items: [],
          subtotal: 0,
          tax: 0,
          total: 0,
          discounts: [],
          updatedAt: new Date().toISOString(),
        };

        set({ cart: clearedCart });
      },

      switchLocation: (newLocationId: string) => {
        const { cart } = get();
        if (!cart) {
          get().initializeCart(newLocationId);
          return;
        }

        // Clear cart when switching locations
        const newCart = {
          ...cart,
          locationId: newLocationId,
          items: [],
          subtotal: 0,
          tax: 0,
          total: 0,
          discounts: [],
          updatedAt: new Date().toISOString(),
        };

        set({ cart: newCart });
      },

      calculateTotals: () => {
        const { cart } = get();
        if (!cart) return;

        const subtotal = cart.items.reduce(
          (sum, item) => sum + (item.price * item.quantity),
          0
        );

        // Apply discounts
        let discountAmount = 0;
        cart.discounts.forEach(discount => {
          if (discount.type === 'percentage') {
            discountAmount += subtotal * (discount.value / 100);
          } else if (discount.type === 'fixed-amount') {
            discountAmount += discount.value;
          }
        });

        const discountedSubtotal = Math.max(0, subtotal - discountAmount);
        const tax = calculateTax(discountedSubtotal, TAX_RATES.recreational);
        const total = discountedSubtotal + tax;

        const updatedCart = {
          ...cart,
          subtotal: discountedSubtotal,
          tax,
          total,
          updatedAt: new Date().toISOString(),
        };

        set({ cart: updatedCart });
      },

      getItemCount: () => {
        const { cart } = get();
        if (!cart) return 0;
        return cart.items.reduce((count, item) => count + item.quantity, 0);
      },

      getItemById: (itemId: string) => {
        const { cart } = get();
        if (!cart) return undefined;
        return cart.items.find(item => item.id === itemId);
      },

      hasItem: (productId: string, variantId: string) => {
        const { cart } = get();
        if (!cart) return false;
        return cart.items.some(
          item => item.productId === productId && item.variantId === variantId
        );
      },
    }),
    {
      name: STORAGE_KEYS.cart,
      partialize: (state) => ({
        cart: state.cart,
      }),
    }
  )
);
