// Base Types
export type ID = string;
export type Timestamp = string;
export type Currency = number;
export type Weight = number;
export type Percentage = number;

// User Types
export interface User {
  id: ID;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  dateOfBirth: string;
  isVerified: boolean;
  membershipTier: MembershipTier;
  preferences: UserPreferences;
  addresses: Address[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface UserPreferences {
  preferredLocation?: ID;
  favoriteCategories: ProductCategory[];
  experienceLevel: ExperienceLevel;
  medicalConditions: MedicalCondition[];
  communicationPreferences: {
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  privacySettings: {
    shareData: boolean;
    analytics: boolean;
  };
}

export interface Address {
  id: ID;
  type: 'home' | 'work' | 'other';
  street: string;
  city: string;
  state: string;
  zipCode: string;
  isDefault: boolean;
}

// Location Types
export interface Location {
  id: ID;
  name: string;
  address: Address;
  phone: string;
  email: string;
  hours: BusinessHours;
  services: LocationService[];
  image: string;
  description: string;
  amenities: string[];
  coordinates: {
    lat: number;
    lng: number;
  };
  isActive: boolean;
}

export interface BusinessHours {
  monday: DayHours;
  tuesday: DayHours;
  wednesday: DayHours;
  thursday: DayHours;
  friday: DayHours;
  saturday: DayHours;
  sunday: DayHours;
}

export interface DayHours {
  open: string;
  close: string;
  isClosed: boolean;
}

export type LocationService = 'recreational' | 'medical' | 'delivery' | 'curbside';

// Product Types
export interface Product {
  id: ID;
  name: string;
  brand: string;
  category: ProductCategory;
  subcategory?: string;
  description: string;
  images: ProductImage[];
  variants: ProductVariant[];
  effects: Effect[];
  terpenes: TerpeneProfile[];
  cannabinoids: CannabinoidProfile[];
  thcContent: Percentage;
  cbdContent: Percentage;
  potencyLevel: PotencyLevel;
  strain?: StrainInfo;
  labResults?: LabResult[];
  tags: string[];
  isActive: boolean;
  isFeatured: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface ProductVariant {
  id: ID;
  name: string;
  size: Weight;
  unit: WeightUnit;
  price: Currency;
  salePrice?: Currency;
  sku: string;
  inventory: InventoryInfo;
  isActive: boolean;
}

export interface ProductImage {
  id: ID;
  url: string;
  alt: string;
  isPrimary: boolean;
  order: number;
}

export interface StrainInfo {
  name: string;
  type: CannabiType;
  genetics: string;
  breeder?: string;
  floweringTime?: string;
  yield?: string;
}

export interface TerpeneProfile {
  name: Terpene;
  percentage: Percentage;
  description: string;
}

export interface CannabinoidProfile {
  name: string;
  percentage: Percentage;
  description: string;
}

export interface LabResult {
  id: ID;
  testType: string;
  testDate: Timestamp;
  labName: string;
  results: Record<string, any>;
  certificateUrl: string;
}

export interface InventoryInfo {
  quantity: number;
  reserved: number;
  available: number;
  lowStockThreshold: number;
  isInStock: boolean;
  lastUpdated: Timestamp;
}

// Cart Types
export interface CartItem {
  id: ID;
  productId: ID;
  variantId: ID;
  quantity: number;
  price: Currency;
  addedAt: Timestamp;
}

export interface Cart {
  id: ID;
  locationId: ID;
  items: CartItem[];
  subtotal: Currency;
  tax: Currency;
  total: Currency;
  discounts: Discount[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Order Types
export interface Order {
  id: ID;
  userId: ID;
  locationId: ID;
  items: OrderItem[];
  status: OrderStatus;
  subtotal: Currency;
  tax: Currency;
  total: Currency;
  discounts: Discount[];
  deliveryMethod: DeliveryMethod;
  deliveryAddress?: Address;
  paymentMethod: PaymentMethod;
  notes?: string;
  estimatedReadyTime?: Timestamp;
  actualReadyTime?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface OrderItem {
  id: ID;
  productId: ID;
  variantId: ID;
  quantity: number;
  price: Currency;
  name: string;
  image: string;
}

// Review Types
export interface Review {
  id: ID;
  userId: ID;
  productId: ID;
  rating: number;
  title: string;
  content: string;
  pros: string[];
  cons: string[];
  wouldRecommend: boolean;
  verifiedPurchase: boolean;
  helpfulVotes: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Discount Types
export interface Discount {
  id: ID;
  code: string;
  type: DiscountType;
  value: number;
  description: string;
  minimumPurchase?: Currency;
  maximumDiscount?: Currency;
  applicableCategories?: ProductCategory[];
  startDate: Timestamp;
  endDate: Timestamp;
  usageLimit?: number;
  usageCount: number;
  isActive: boolean;
}

// Newsletter Types
export interface NewsletterSubscription {
  id: ID;
  email: string;
  firstName?: string;
  lastName?: string;
  preferences: {
    deals: boolean;
    newProducts: boolean;
    events: boolean;
    education: boolean;
  };
  isActive: boolean;
  subscribedAt: Timestamp;
  unsubscribedAt?: Timestamp;
}

// Contact Types
export interface ContactMessage {
  id: ID;
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  category: 'general' | 'support' | 'complaint' | 'suggestion';
  status: 'new' | 'in-progress' | 'resolved' | 'closed';
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Event Types
export interface Event {
  id: ID;
  title: string;
  description: string;
  image: string;
  startDate: Timestamp;
  endDate: Timestamp;
  location: string;
  category: 'education' | 'promotion' | 'community' | 'product-launch';
  isActive: boolean;
  registrationRequired: boolean;
  maxAttendees?: number;
  currentAttendees: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Blog Types
export interface BlogPost {
  id: ID;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  featuredImage: string;
  author: Author;
  category: BlogCategory;
  tags: string[];
  isPublished: boolean;
  publishedAt?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface Author {
  id: ID;
  name: string;
  bio: string;
  avatar: string;
  socialLinks: Record<string, string>;
}

// Enum Types
export type ProductCategory = 
  | 'flower'
  | 'pre-rolls'
  | 'concentrates'
  | 'edibles'
  | 'tinctures'
  | 'topicals'
  | 'vaporizers'
  | 'accessories'
  | 'apparel';

export type CannabiType = 'sativa' | 'indica' | 'hybrid' | 'cbd' | 'high-cbd';

export type Effect = 
  | 'relaxing'
  | 'energizing'
  | 'euphoric'
  | 'creative'
  | 'focused'
  | 'sleepy'
  | 'happy'
  | 'uplifted'
  | 'calm'
  | 'pain-relief'
  | 'anti-inflammatory'
  | 'appetite-stimulant';

export type Terpene = 
  | 'myrcene'
  | 'limonene'
  | 'pinene'
  | 'linalool'
  | 'caryophyllene'
  | 'humulene'
  | 'terpinolene'
  | 'ocimene'
  | 'bisabolol'
  | 'valencene';

export type PotencyLevel = 'low' | 'medium' | 'high' | 'very-high';

export type ExperienceLevel = 'beginner' | 'intermediate' | 'experienced' | 'expert';

export type MedicalCondition = 
  | 'chronic-pain'
  | 'anxiety'
  | 'depression'
  | 'insomnia'
  | 'epilepsy'
  | 'cancer'
  | 'glaucoma'
  | 'ptsd'
  | 'arthritis'
  | 'migraines'
  | 'nausea'
  | 'appetite-loss';

export type WeightUnit = 'g' | 'oz';

export type OrderStatus = 
  | 'pending'
  | 'confirmed'
  | 'preparing'
  | 'ready'
  | 'completed'
  | 'cancelled';

export type DeliveryMethod = 'pickup' | 'delivery' | 'curbside';

export type PaymentMethod = 
  | 'cash'
  | 'debit'
  | 'credit'
  | 'digital-wallet'
  | 'cryptocurrency';

export type MembershipTier = 'bronze' | 'silver' | 'gold' | 'platinum';

export type DiscountType = 
  | 'percentage'
  | 'fixed-amount'
  | 'buy-one-get-one'
  | 'bulk-discount'
  | 'loyalty-points';

export type BlogCategory = 
  | 'education'
  | 'news'
  | 'strain-reviews'
  | 'wellness'
  | 'recipes'
  | 'industry';

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Form Types
export interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  category: string;
}

export interface NewsletterFormData {
  email: string;
  firstName?: string;
  lastName?: string;
  preferences: {
    deals: boolean;
    newProducts: boolean;
    events: boolean;
    education: boolean;
  };
}

export interface AgeVerificationData {
  dateOfBirth: string;
  isVerified: boolean;
}

// Component Props Types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface ButtonProps extends BaseComponentProps {
  variant?: 'primary' | 'secondary' | 'accent' | 'ghost' | 'link';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
}

export interface CardProps extends BaseComponentProps {
  variant?: 'default' | 'product' | 'location' | 'testimonial';
  hover?: boolean;
  padding?: 'sm' | 'md' | 'lg';
}

export interface ModalProps extends BaseComponentProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

// State Types
export interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  selectedLocation: Location | null;
  cart: Cart | null;
  ageVerified: boolean;
  preferences: UserPreferences;
}

export interface UIState {
  isMobileMenuOpen: boolean;
  isAgeGateOpen: boolean;
  isLocationSelectorOpen: boolean;
  loading: boolean;
  error: string | null;
}
