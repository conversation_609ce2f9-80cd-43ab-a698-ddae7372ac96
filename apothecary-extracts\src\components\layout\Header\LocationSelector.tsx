'use client';

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, ModalBody } from '@/components/ui/Modal';
import { Button } from '@/components/ui/Button';
import { Card, CardContent } from '@/components/ui/Card';
import { Icon } from '@/components/ui/Icon';
import { useUIStore } from '@/store/uiStore';
import { useLocation } from '@/hooks/useLocation';

export const LocationSelector: React.FC = () => {
  const { isLocationSelectorOpen, setLocationSelectorOpen } = useUIStore();
  const { 
    selectedLocation, 
    availableLocations, 
    handleLocationChange,
    locationStatus,
    hasService 
  } = useLocation();

  const handleLocationSelect = (location: any) => {
    const success = handleLocationChange(location);
    if (success) {
      setLocationSelectorOpen(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'text-success';
      case 'closed':
        return 'text-error';
      case 'opening-soon':
        return 'text-warning';
      default:
        return 'text-neutral-charcoal/60';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'open':
        return 'Open Now';
      case 'closed':
        return 'Closed';
      case 'opening-soon':
        return 'Opening Soon';
      default:
        return 'Unknown';
    }
  };

  return (
    <Modal
      isOpen={isLocationSelectorOpen}
      onClose={() => setLocationSelectorOpen(false)}
      title="Select Your Location"
      description="Choose a dispensary location to view products and place orders"
      size="lg"
    >
      <ModalBody>
        <div className="space-y-4">
          {availableLocations.map((location) => {
            const isSelected = selectedLocation?.id === location.id;
            const status = isSelected ? locationStatus : 'closed'; // Simplified for demo
            
            return (
              <Card
                key={location.id}
                variant={isSelected ? 'elevated' : 'default'}
                padding="md"
                className={`cursor-pointer transition-all duration-200 ${
                  isSelected 
                    ? 'ring-2 ring-primary-sage border-primary-sage' 
                    : 'hover:shadow-md'
                }`}
                onClick={() => handleLocationSelect(location)}
              >
                <CardContent className="p-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="text-lg font-semibold text-neutral-charcoal">
                          {location.name}
                        </h3>
                        {isSelected && (
                          <Icon name="check" size="sm" className="text-primary-sage" />
                        )}
                      </div>
                      
                      <div className="space-y-2 text-sm text-neutral-charcoal/70">
                        <div className="flex items-center space-x-2">
                          <Icon name="map-pin" size="sm" />
                          <span>
                            {location.address.street}, {location.address.city}, {location.address.state} {location.address.zipCode}
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Icon name="phone" size="sm" />
                          <span>{location.phone}</span>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Icon name="clock" size="sm" />
                          <span className={getStatusColor(status)}>
                            {getStatusText(status)}
                          </span>
                        </div>
                      </div>
                      
                      {/* Services */}
                      <div className="flex flex-wrap gap-2 mt-3">
                        {location.services.map((service) => (
                          <span
                            key={service}
                            className="inline-flex items-center px-2 py-1 text-xs font-medium bg-primary-sage/10 text-primary-sage rounded-full"
                          >
                            {service === 'recreational' && 'Recreational'}
                            {service === 'medical' && 'Medical'}
                            {service === 'delivery' && 'Delivery'}
                            {service === 'curbside' && 'Curbside'}
                          </span>
                        ))}
                      </div>
                    </div>
                    
                    <div className="ml-4">
                      <Button
                        variant={isSelected ? 'primary' : 'secondary'}
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleLocationSelect(location);
                        }}
                      >
                        {isSelected ? 'Selected' : 'Select'}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
        
        {availableLocations.length === 0 && (
          <div className="text-center py-8">
            <Icon name="map-pin" size="xl" className="text-neutral-charcoal/30 mx-auto mb-4" />
            <p className="text-neutral-charcoal/60">No locations available at this time.</p>
          </div>
        )}
      </ModalBody>
    </Modal>
  );
};

export default LocationSelector;
