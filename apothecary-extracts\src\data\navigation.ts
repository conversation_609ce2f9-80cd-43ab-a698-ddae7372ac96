export interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
  icon?: string;
  description?: string;
}

export const mainNavigation: NavigationItem[] = [
  {
    label: 'Shop',
    href: '/shop',
    children: [
      {
        label: 'Flower',
        href: '/shop/flower',
        description: 'Premium cannabis flower strains',
      },
      {
        label: 'Pre-rolls',
        href: '/shop/pre-rolls',
        description: 'Ready-to-smoke joints and blunts',
      },
      {
        label: 'Concentrates',
        href: '/shop/concentrates',
        description: 'High-potency extracts and dabs',
      },
      {
        label: 'Edibles',
        href: '/shop/edibles',
        description: 'Cannabis-infused foods and beverages',
      },
      {
        label: 'Tinctures',
        href: '/shop/tinctures',
        description: 'Liquid cannabis extracts',
      },
      {
        label: 'Topicals',
        href: '/shop/topicals',
        description: 'Cannabis-infused creams and balms',
      },
      {
        label: 'Vaporizers',
        href: '/shop/vaporizers',
        description: 'Vape pens and cartridges',
      },
      {
        label: 'Accessories',
        href: '/shop/accessories',
        description: 'Smoking and storage accessories',
      },
    ],
  },
  {
    label: 'About',
    href: '/about',
    children: [
      {
        label: 'Our Story',
        href: '/about',
        description: 'Learn about Apothecary Extracts',
      },
      {
        label: 'Blog',
        href: '/blog',
        description: 'Cannabis education and news',
      },
      {
        label: 'Careers',
        href: '/careers',
        description: 'Join our team',
      },
      {
        label: 'Contact',
        href: '/contact',
        description: 'Get in touch with us',
      },
    ],
  },
  {
    label: 'Locations',
    href: '/locations',
    children: [
      {
        label: 'All Locations',
        href: '/locations',
        description: 'Find a dispensary near you',
      },
      {
        label: 'Downtown Boston',
        href: '/locations/downtown-boston',
        description: '123 Main Street, Boston, MA',
      },
      {
        label: 'Cambridge',
        href: '/locations/cambridge',
        description: '456 Harvard Avenue, Cambridge, MA',
      },
    ],
  },
  {
    label: 'Featured',
    href: '/featured',
  },
  {
    label: 'Events',
    href: '/events',
  },
  {
    label: 'Blog',
    href: '/blog',
  },
];

export const footerNavigation = {
  products: [
    { label: 'Shop All', href: '/shop' },
    { label: 'Flower', href: '/shop/flower' },
    { label: 'Pre-rolls', href: '/shop/pre-rolls' },
    { label: 'Concentrates', href: '/shop/concentrates' },
    { label: 'Edibles', href: '/shop/edibles' },
    { label: 'Tinctures', href: '/shop/tinctures' },
    { label: 'Topicals', href: '/shop/topicals' },
    { label: 'Vaporizers', href: '/shop/vaporizers' },
    { label: 'Accessories', href: '/shop/accessories' },
  ],
  company: [
    { label: 'About Us', href: '/about' },
    { label: 'Our Story', href: '/about#story' },
    { label: 'Careers', href: '/careers' },
    { label: 'Contact', href: '/contact' },
    { label: 'Blog', href: '/blog' },
    { label: 'Events', href: '/events' },
  ],
  support: [
    { label: 'Help Center', href: '/help' },
    { label: 'FAQs', href: '/faq' },
    { label: 'Shipping Info', href: '/shipping' },
    { label: 'Returns', href: '/returns' },
    { label: 'Track Order', href: '/track-order' },
  ],
  legal: [
    { label: 'Privacy Policy', href: '/privacy' },
    { label: 'Terms of Service', href: '/terms' },
    { label: 'Cookie Policy', href: '/cookies' },
    { label: 'Compliance', href: '/compliance' },
  ],
};
