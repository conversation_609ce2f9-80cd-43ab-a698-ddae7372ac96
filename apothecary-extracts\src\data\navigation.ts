export interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
  icon?: string;
  description?: string;
}

export const mainNavigation: NavigationItem[] = [
  {
    label: 'Shop',
    href: '/shop',
    children: [
      {
        label: 'Apothecary Extracts™ Concentrates',
        href: '/shop/concentrates',
        description: 'Award-winning live resin, live rosin, shatter',
      },
      {
        label: 'Flower',
        href: '/shop/flower',
        description: 'Premium cannabis flower strains',
      },
      {
        label: 'Pre-rolls',
        href: '/shop/pre-rolls',
        description: 'Traditional and concentrate-infused joints',
      },
      {
        label: 'Vape Cartridges',
        href: '/shop/vaporizers',
        description: 'Premium cartridges with our concentrates',
      },
      {
        label: 'Edibles',
        href: '/shop/edibles',
        description: 'Cannabis-infused foods and beverages',
      },
      {
        label: 'Tinctures',
        href: '/shop/tinctures',
        description: 'Liquid cannabis extracts',
      },
      {
        label: 'Topicals',
        href: '/shop/topicals',
        description: 'Cannabis-infused creams and balms',
      },
      {
        label: 'Accessories',
        href: '/shop/accessories',
        description: 'Dabbing tools and storage solutions',
      },
    ],
  },
  {
    label: 'About',
    href: '/about',
    children: [
      {
        label: 'Our Story',
        href: '/about',
        description: 'Learn about Apothecary Farms',
      },
      {
        label: 'Blog',
        href: '/blog',
        description: 'Cannabis education and news',
      },
      {
        label: 'Careers',
        href: '/careers',
        description: 'Join our team',
      },
      {
        label: 'Contact',
        href: '/contact',
        description: 'Get in touch with us',
      },
    ],
  },
  {
    label: 'Locations',
    href: '/locations',
    children: [
      {
        label: 'All Locations',
        href: '/locations',
        description: 'Find a dispensary near you',
      },
      {
        label: 'Colorado Springs (Flagship)',
        href: '/locations/colorado-springs',
        description: '3049 Delta Dr, Colorado Springs, CO',
      },
      {
        label: 'Denver',
        href: '/locations/denver',
        description: '2251 S Broadway, Denver, CO',
      },
      {
        label: 'Pueblo',
        href: '/locations/pueblo',
        description: '1917 Santa Fe Dr, Pueblo, CO',
      },
      {
        label: 'Oklahoma City',
        href: '/locations/oklahoma-city',
        description: '7941 N May Ave, Oklahoma City, OK',
      },
      {
        label: 'Tulsa',
        href: '/locations/tulsa',
        description: '4942 S Union Ave, Tulsa, OK',
      },
      {
        label: 'Moore',
        href: '/locations/moore',
        description: '2100 Riverwalk Dr Suite A, Moore, OK',
      },
    ],
  },
  {
    label: 'Featured',
    href: '/featured',
  },
  {
    label: 'Events',
    href: '/events',
  },
  {
    label: 'Blog',
    href: '/blog',
  },
];

export const footerNavigation = {
  products: [
    { label: 'Shop All', href: '/shop' },
    { label: 'Apothecary Extracts™', href: '/shop/concentrates' },
    { label: 'Flower', href: '/shop/flower' },
    { label: 'Pre-rolls', href: '/shop/pre-rolls' },
    { label: 'Vape Cartridges', href: '/shop/vaporizers' },
    { label: 'Edibles', href: '/shop/edibles' },
    { label: 'Tinctures', href: '/shop/tinctures' },
    { label: 'Topicals', href: '/shop/topicals' },
    { label: 'Accessories', href: '/shop/accessories' },
  ],
  company: [
    { label: 'About Us', href: '/about' },
    { label: 'Our Story', href: '/about#story' },
    { label: 'Careers', href: '/careers' },
    { label: 'Contact', href: '/contact' },
    { label: 'Blog', href: '/blog' },
    { label: 'Events', href: '/events' },
  ],
  support: [
    { label: 'Help Center', href: '/help' },
    { label: 'FAQs', href: '/faq' },
    { label: 'Shipping Info', href: '/shipping' },
    { label: 'Returns', href: '/returns' },
    { label: 'Track Order', href: '/track-order' },
  ],
  legal: [
    { label: 'Privacy Policy', href: '/privacy' },
    { label: 'Terms of Service', href: '/terms' },
    { label: 'Cookie Policy', href: '/cookies' },
    { label: 'Compliance', href: '/compliance' },
  ],
};
